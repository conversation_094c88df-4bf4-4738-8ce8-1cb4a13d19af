'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import BlogStyleDisplay from '@/components/BlogStyleDisplay'

export default function BlogDisplayByIdPage() {
  const params = useParams()
  const [content, setContent] = useState('')
  const [title, setTitle] = useState('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [savedToDatabase, setSavedToDatabase] = useState(false)
  const [databaseId, setDatabaseId] = useState<string | undefined>()

  useEffect(() => {
    const fetchContent = async () => {
      try {
        // First try to fetch from database using the ID
        const dbResponse = await fetch(`/api/content?id=${params.id}`)
        if (dbResponse.ok) {
          const dbData = await dbResponse.json()
          if (dbData.content && dbData.content.length > 0) {
            const item = dbData.content[0]
            setContent(item.content)
            setTitle(item.title)
            setSavedToDatabase(true)
            setDatabaseId(item.id)
            setLoading(false)
            return
          }
        }

        // Fallback to cache if not found in database
        const response = await fetch(`/api/blog-content/${params.id}`)
        if (!response.ok) {
          throw new Error('Content not found')
        }
        const data = await response.json()
        setContent(data.content)
        setTitle(data.title)
        setSavedToDatabase(false)
      } catch (error) {
        console.error('Error fetching content:', error)
        setError('Content not found or expired')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchContent()
    }
  }, [params.id])

  const handleContentChange = (newContent: string) => {
    setContent(newContent)
  }

  if (loading) {
    return (
      <div className="min-h-screen sky-bg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-sky-500 mx-auto mb-4"></div>
          <p className="sky-text">Loading your blog post...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen sky-bg flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">😕</div>
          <h1 className="text-2xl font-bold sky-heading mb-2">Content Not Found</h1>
          <p className="sky-text mb-6">{error}</p>
          <Link href="/blog-generator">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-6 py-3 bg-sky-500/80 backdrop-blur-sm hover:bg-sky-600/90 text-white rounded-xl transition-all shadow-lg border border-white/20"
            >
              Generate New Blog Post
            </motion.button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="relative">
      {/* Back Button */}
      <div className="absolute top-4 left-4 z-20">
        <Link href="/blog-generator">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="p-3 bg-white/30 backdrop-blur-sm hover:bg-white/40 border border-sky-200/50 rounded-xl transition-all shadow-lg"
          >
            <ArrowLeft className="w-5 h-5 text-sky-700" />
          </motion.button>
        </Link>
      </div>

      {/* Blog Display Component */}
      <BlogStyleDisplay
        content={content}
        title={title}
        onContentChange={handleContentChange}
        editable={true}
        savedToDatabase={savedToDatabase}
        databaseId={databaseId}
        showLibraryLink={true}
      />
    </div>
  )
}
