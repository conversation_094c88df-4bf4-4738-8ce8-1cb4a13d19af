import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { TavilySearchService } from '@/lib/search'
import { NodeWebScraperService } from '@/lib/web-scraper'
import { GeminiService } from '@/lib/gemini'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { topic, wordCount = 2000, tone = 'professional' } = await request.json()

    if (!topic) {
      return NextResponse.json(
        { error: 'Topic is required' },
        { status: 400 }
      )
    }

    // Create a streaming response
    const encoder = new TextEncoder()
    const stream = new ReadableStream({
      async start(controller) {
        const sendUpdate = (data: any) => {
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(data)}\n\n`))
        }

        try {
          // Step 1: Search for content
          sendUpdate({ step: 'Searching for relevant content...', progress: 10 })
          
          const searchService = new TavilySearchService()
          const searchResults = await searchService.search(topic, 10, {
            searchDepth: 'advanced',
            prioritizeRecent: true,
            temporalFocus: 'current'
          })

          if (searchResults.items.length === 0) {
            sendUpdate({ error: 'No search results found for the topic' })
            controller.close()
            return
          }

          sendUpdate({ step: 'Found search results, extracting content...', progress: 30 })

          // Step 2: Extract content from URLs
          const scraper = new NodeWebScraperService()
          const extractedContent = await scraper.scrapeMultipleUrls(
            searchResults.items.slice(0, 10).map(r => r.url),
            {
              timeout: 15000,
              maxLength: 8000
            }
          )

          const successfulExtractions = extractedContent.filter(content => content.success)
          
          if (successfulExtractions.length === 0) {
            sendUpdate({ error: 'Failed to extract content from any pages' })
            controller.close()
            return
          }

          sendUpdate({ step: 'Analyzing competition...', progress: 60 })

          // Step 3: Competitive Analysis with Gemini
          const gemini = new GeminiService()
          
          const competitiveAnalysisPrompt = `
Analyze the following content about "${topic}" and provide a competitive analysis:

${successfulExtractions.map((content, index) => `
Source ${index + 1}: ${content.url}
Title: ${content.title}
Content: ${content.content.substring(0, 2000)}...
`).join('\n\n')}

Provide:
1. Key themes and topics covered
2. Content gaps and opportunities
3. Unique angles not well covered
4. Writing style patterns
5. Recommended content structure

Keep the analysis concise and actionable.`

          const competitiveAnalysis = await gemini.generateContent(
            competitiveAnalysisPrompt,
            {
              maxOutputTokens: 2000,
              thinkingConfig: {
                thinkingBudget: -1,
                includeThoughts: false
              }
            }
          )

          sendUpdate({ step: 'Generating blog content...', progress: 80 })

          // Step 4: Generate the blog post
          const contentGenerationPrompt = `
You are a world-class professional content writer. Create a comprehensive, engaging blog post about "${topic}".

COMPETITIVE ANALYSIS:
${competitiveAnalysis.response}

EXTRACTED RESEARCH DATA:
${successfulExtractions.map((content, index) => `
Source ${index + 1}: ${content.title}
Key Content: ${content.content.substring(0, 1500)}...
`).join('\n\n')}

REQUIREMENTS:
- Write exactly ${wordCount} words
- Use a ${tone} tone
- Create original, superior content that fills gaps identified in the competitive analysis
- Include data, statistics, and insights from the research
- Structure with clear headings and subheadings
- Make it engaging and valuable to readers
- Reference 2025 trends and current information
- Include actionable takeaways

Write the complete blog post in markdown format with proper headings, bullet points, and formatting.`

          const blogPost = await gemini.generateContent(
            contentGenerationPrompt,
            {
              maxOutputTokens: 8000,
              thinkingConfig: {
                thinkingBudget: -1,
                includeThoughts: false
              }
            }
          )

          sendUpdate({ step: 'Blog post generated successfully!', progress: 100 })

          const finalContent = blogPost.response || ''
          const actualWordCount = finalContent.split(/\s+/).filter(w => w.length > 0).length

          // Save content to database
          let savedContent = null
          try {
            savedContent = await prisma.content.create({
              data: {
                userId: session.user.id,
                type: 'blog',
                title: topic,
                content: finalContent,
                wordCount: actualWordCount,
                tone: tone || 'professional',
                metadata: JSON.stringify({
                  sourcesUsed: successfulExtractions.length,
                  searchResults: searchResults.items.length,
                  generatedAt: new Date().toISOString(),
                  generationMethod: 'blog-generator-stream'
                }),
                status: 'published'
              }
            })
            console.log('✅ Content saved to database with ID:', savedContent.id)
          } catch (dbError) {
            console.error('❌ Failed to save content to database:', dbError)
            // Continue with in-memory cache as fallback
          }

          // Generate a unique ID for cache (fallback)
          const contentId = savedContent?.id || Math.random().toString(36).substr(2, 9)

          // Store content in cache for immediate access
          global.contentCache = global.contentCache || new Map()
          global.contentCache.set(contentId, {
            content: finalContent,
            title: topic,
            createdAt: Date.now()
          })

          // Clean up old cache entries (older than 1 hour)
          const oneHourAgo = Date.now() - (60 * 60 * 1000)
          for (const [key, value] of global.contentCache.entries()) {
            if (value.createdAt < oneHourAgo) {
              global.contentCache.delete(key)
            }
          }

          // Send the final content with database ID or cache ID
          sendUpdate({
            content: finalContent,
            contentId: contentId,
            redirectUrl: `/blog-display/${contentId}`,
            savedToDatabase: !!savedContent,
            databaseId: savedContent?.id,
            metadata: {
              wordCount: actualWordCount,
              sourcesUsed: successfulExtractions.length,
              searchResults: searchResults.items.length
            }
          })

        } catch (error) {
          console.error('Blog generation error:', error)
          sendUpdate({ 
            error: error instanceof Error ? error.message : 'Generation failed' 
          })
        } finally {
          controller.close()
        }
      }
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
