/**
 * Content Writer API Route - KaibanJS + OpenRouter <PERSON><PERSON>
 * Handles content generation requests using the new KaibanJS structure
 */
import { NextRequest, NextResponse } from 'next/server';
import { executeContentWorkflow, validateTeamConfiguration } from '@/lib/agents/content-team/contentTeam';

// Interface for content generation request
interface ContentGenerationRequest {
  topic: string;
  wordCount?: number;
  tone?: string;
  contentType?: string;
  targetAudience?: string;
}

export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body: ContentGenerationRequest = await request.json();

    // Validate required fields
    if (!body.topic) {
      return NextResponse.json(
        { error: 'Missing required field: topic' },
        { status: 400 }
      );
    }

    // Validate team configuration
    const validation = validateTeamConfiguration();
    if (!validation.isValid) {
      return NextResponse.json(
        {
          error: 'Team configuration invalid',
          issues: validation.issues,
          warnings: validation.warnings
        },
        { status: 500 }
      );
    }

    console.log('🚀 Starting content generation for:', body.topic);

    // Execute the content workflow
    const result = await executeContentWorkflow({
      topic: body.topic,
      wordCount: body.wordCount || 2000,
      tone: body.tone || 'professional',
      contentType: body.contentType || 'blog-post',
      targetAudience: body.targetAudience || 'general'
    });

    if (result.success) {
      return NextResponse.json({
        success: true,
        content: result.content,
        metadata: result.metadata
      });
    } else {
      return NextResponse.json(
        {
          error: 'Content generation failed',
          details: result.error
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Content generation API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS preflight
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Accept',
    },
  });
}