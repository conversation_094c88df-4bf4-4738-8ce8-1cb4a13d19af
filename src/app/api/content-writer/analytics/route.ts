/**
 * Content Writer Analytics API Route
 * Provides analytics and performance data for content generation
 */
import { NextRequest, NextResponse } from 'next/server';

// Mock analytics data structure
interface AnalyticsData {
  totalProjects: number;
  completedProjects: number;
  totalCost: number;
  averageCostPerProject: number;
  averageGenerationTime: number;
  successRate: number;
  popularContentTypes: { type: string; count: number }[];
  monthlyUsage: { month: string; projects: number; cost: number }[];
  agentPerformance: {
    agentName: string;
    averageTime: number;
    successRate: number;
    totalTasks: number;
  }[];
}

// Mock data for demonstration
const mockAnalytics: AnalyticsData = {
  totalProjects: 247,
  completedProjects: 238,
  totalCost: 89.64,
  averageCostPerProject: 0.363,
  averageGenerationTime: 156, // seconds
  successRate: 96.4,
  popularContentTypes: [
    { type: 'blog-post', count: 156 },
    { type: 'article', count: 45 },
    { type: 'landing-page', count: 28 },
    { type: 'social-media', count: 12 },
    { type: 'email-newsletter', count: 6 }
  ],
  monthlyUsage: [
    { month: '2024-01', projects: 45, cost: 16.32 },
    { month: '2024-02', projects: 52, cost: 18.89 },
    { month: '2024-03', projects: 61, cost: 22.11 },
    { month: '2024-04', projects: 58, cost: 21.05 },
    { month: '2024-05', projects: 31, cost: 11.27 } // Current month (partial)
  ],
  agentPerformance: [
    {
      agentName: 'Research Agent',
      averageTime: 28,
      successRate: 99.2,
      totalTasks: 247
    },
    {
      agentName: 'SEO Strategist',
      averageTime: 22,
      successRate: 97.8,
      totalTasks: 247
    },
    {
      agentName: 'Content Architect',
      averageTime: 35,
      successRate: 98.4,
      totalTasks: 247
    },
    {
      agentName: 'Content Creator',
      averageTime: 89,
      successRate: 94.1,
      totalTasks: 247
    },
    {
      agentName: 'Quality Editor',
      averageTime: 31,
      successRate: 98.8,
      totalTasks: 238 // Some projects didn't reach final review
    }
  ]
};

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '30d';
    const metric = searchParams.get('metric');

    // Filter analytics based on query parameters
    let filteredAnalytics = { ...mockAnalytics };

    if (metric) {
      // Return specific metric
      switch (metric) {
        case 'costs':
          return NextResponse.json({
            totalCost: filteredAnalytics.totalCost,
            averageCostPerProject: filteredAnalytics.averageCostPerProject,
            monthlyUsage: filteredAnalytics.monthlyUsage,
            costTrends: calculateCostTrends(filteredAnalytics.monthlyUsage)
          });
        
        case 'performance':
          return NextResponse.json({
            averageGenerationTime: filteredAnalytics.averageGenerationTime,
            successRate: filteredAnalytics.successRate,
            agentPerformance: filteredAnalytics.agentPerformance,
            performanceTrends: calculatePerformanceTrends()
          });
        
        case 'usage':
          return NextResponse.json({
            totalProjects: filteredAnalytics.totalProjects,
            completedProjects: filteredAnalytics.completedProjects,
            popularContentTypes: filteredAnalytics.popularContentTypes,
            monthlyUsage: filteredAnalytics.monthlyUsage
          });
        
        default:
          return NextResponse.json(
            { error: 'Invalid metric. Available metrics: costs, performance, usage' },
            { status: 400 }
          );
      }
    }

    // Apply time range filtering (simplified for demo)
    if (timeRange !== '30d') {
      filteredAnalytics = applyTimeRangeFilter(filteredAnalytics, timeRange);
    }

    // Return full analytics data
    return NextResponse.json({
      success: true,
      data: {
        ...filteredAnalytics,
        generatedAt: new Date().toISOString(),
        timeRange,
        insights: generateInsights(filteredAnalytics)
      }
    });

  } catch (error) {
    console.error('Analytics API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics data', details: error.message },
      { status: 500 }
    );
  }
}

function calculateCostTrends(monthlyUsage: { month: string; projects: number; cost: number }[]) {
  const trends = [];
  for (let i = 1; i < monthlyUsage.length; i++) {
    const current = monthlyUsage[i];
    const previous = monthlyUsage[i - 1];
    const costChange = ((current.cost - previous.cost) / previous.cost) * 100;
    const projectChange = ((current.projects - previous.projects) / previous.projects) * 100;
    
    trends.push({
      month: current.month,
      costChangePercent: parseFloat(costChange.toFixed(2)),
      projectChangePercent: parseFloat(projectChange.toFixed(2)),
      efficiency: parseFloat((current.cost / current.projects).toFixed(3))
    });
  }
  return trends;
}

function calculatePerformanceTrends() {
  // Mock performance trends for demonstration
  return [
    { metric: 'Average Generation Time', trend: -8.5, unit: 'seconds' },
    { metric: 'Success Rate', trend: 2.1, unit: 'percent' },
    { metric: 'Cost per Project', trend: -15.3, unit: 'percent' },
    { metric: 'Quality Score', trend: 4.7, unit: 'points' }
  ];
}

function applyTimeRangeFilter(analytics: AnalyticsData, timeRange: string) {
  // Simplified time range filtering for demonstration
  switch (timeRange) {
    case '7d':
      return {
        ...analytics,
        totalProjects: Math.floor(analytics.totalProjects * 0.3),
        completedProjects: Math.floor(analytics.completedProjects * 0.3),
        totalCost: analytics.totalCost * 0.3,
        monthlyUsage: analytics.monthlyUsage.slice(-1)
      };
    case '90d':
      return analytics; // Return all data for 90 days
    default:
      return analytics;
  }
}

function generateInsights(analytics: AnalyticsData): string[] {
  const insights = [];
  
  // Cost efficiency insight
  if (analytics.averageCostPerProject < 0.5) {
    insights.push('🎯 Excellent cost efficiency! Your average cost per project is 85-95% lower than premium alternatives.');
  }
  
  // Success rate insight
  if (analytics.successRate > 95) {
    insights.push('✨ Outstanding success rate! Your content generation workflow is highly reliable.');
  }
  
  // Usage pattern insight
  const blogPostRatio = analytics.popularContentTypes.find(t => t.type === 'blog-post')?.count / analytics.totalProjects;
  if (blogPostRatio && blogPostRatio > 0.6) {
    insights.push('📝 Blog posts dominate your content creation, consider exploring other formats for variety.');
  }
  
  // Performance insight
  if (analytics.averageGenerationTime < 180) {
    insights.push('⚡ Fast generation times! Your workflow completes most projects in under 3 minutes.');
  }
  
  // Growth insight
  const recentMonth = analytics.monthlyUsage[analytics.monthlyUsage.length - 1];
  const previousMonth = analytics.monthlyUsage[analytics.monthlyUsage.length - 2];
  if (recentMonth && previousMonth && recentMonth.projects > previousMonth.projects) {
    insights.push('📈 Your content creation is accelerating! Usage increased this month.');
  }
  
  return insights;
}

// Handle OPTIONS for CORS preflight
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}

export const dynamic = 'force-dynamic';