import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')
    const id = searchParams.get('id')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Build where clause
    const where: any = {
      userId: session.user.id
    }

    if (id) {
      where.id = id
    }

    if (type && type !== 'all') {
      where.type = type
    }

    // Fetch content with pagination
    const [content, totalCount] = await Promise.all([
      prisma.content.findMany({
        where,
        orderBy: {
          createdAt: 'desc'
        },
        take: limit,
        skip: offset,
        select: {
          id: true,
          type: true,
          title: true,
          content: true,
          wordCount: true,
          tone: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          metadata: true
        }
      }),
      prisma.content.count({ where })
    ])

    // Parse metadata for each content item
    const contentWithParsedMetadata = content.map((item: any) => ({
      ...item,
      metadata: item.metadata ? JSON.parse(item.metadata) : null,
      preview: item.content.substring(0, 200) + (item.content.length > 200 ? '...' : '')
    }))

    return NextResponse.json({
      success: true,
      content: contentWithParsedMetadata,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      }
    })

  } catch (error) {
    console.error('Content fetch error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch content' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const contentId = searchParams.get('id')
    const bulk = searchParams.get('bulk')

    // Handle bulk deletion
    if (bulk) {
      try {
        const body = await request.json()
        const { contentIds } = body

        if (!Array.isArray(contentIds) || contentIds.length === 0) {
          return NextResponse.json(
            { error: 'Content IDs array is required for bulk delete' },
            { status: 400 }
          )
        }

        // Verify ownership and delete
        const deletedContent = await prisma.content.deleteMany({
          where: {
            id: { in: contentIds },
            userId: session.user.id // Ensure user owns the content
          }
        })

        return NextResponse.json({
          success: true,
          message: `${deletedContent.count} content items deleted successfully`,
          deletedCount: deletedContent.count
        })
      } catch (error) {
        return NextResponse.json(
          { error: 'Invalid request body for bulk delete' },
          { status: 400 }
        )
      }
    }

    // Handle single deletion
    if (!contentId) {
      return NextResponse.json(
        { error: 'Content ID is required' },
        { status: 400 }
      )
    }

    // Verify ownership and delete
    const deletedContent = await prisma.content.deleteMany({
      where: {
        id: contentId,
        userId: session.user.id // Ensure user owns the content
      }
    })

    if (deletedContent.count === 0) {
      return NextResponse.json(
        { error: 'Content not found or unauthorized' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Content deleted successfully'
    })

  } catch (error) {
    console.error('Content deletion error:', error)
    return NextResponse.json(
      { error: 'Failed to delete content' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const contentId = searchParams.get('id')
    const action = searchParams.get('action')

    if (!contentId) {
      return NextResponse.json(
        { error: 'Content ID is required' },
        { status: 400 }
      )
    }

    // Handle different actions
    if (action === 'favorite') {
      const body = await request.json()
      const { isFavorite } = body

      // Update content metadata to mark as favorite
      const updatedContent = await prisma.content.updateMany({
        where: {
          id: contentId,
          userId: session.user.id
        },
        data: {
          metadata: JSON.stringify({
            ...JSON.parse('{}'),
            isFavorite: Boolean(isFavorite),
            updatedAt: new Date().toISOString()
          })
        }
      })

      if (updatedContent.count === 0) {
        return NextResponse.json(
          { error: 'Content not found or unauthorized' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        message: isFavorite ? 'Content marked as favorite' : 'Content unfavorited'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Content update error:', error)
    return NextResponse.json(
      { error: 'Failed to update content' },
      { status: 500 }
    )
  }
} 