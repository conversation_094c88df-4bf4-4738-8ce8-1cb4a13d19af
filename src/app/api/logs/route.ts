/**
 * Logs API - Development endpoint for viewing V2 system logs
 */

import { NextRequest, NextResponse } from 'next/server';
import { logger } from '@/lib/agents/v2/utils/logger';

export async function GET(request: NextRequest) {
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Logs endpoint only available in development' },
      { status: 403 }
    );
  }

  const { searchParams } = new URL(request.url);
  const level = searchParams.get('level');
  const agent = searchParams.get('agent');
  const count = parseInt(searchParams.get('count') || '50');
  const format = searchParams.get('format') || 'json';

  try {
    let logs = logger.getRecentLogs(count);

    // Filter by level if specified
    if (level) {
      logs = logs.filter(log => log.level.toString() === level);
    }

    // Filter by agent if specified
    if (agent) {
      logs = logs.filter(log => log.context.agentName === agent);
    }

    // Return stats if requested
    if (searchParams.get('stats') === 'true') {
      const stats = logger.getSessionStats();
      return NextResponse.json({
        stats,
        totalLogs: logs.length,
        availableAgents: Array.from(new Set(
          logger.getRecentLogs(1000).map(log => log.context.agentName).filter(Boolean)
        )),
      });
    }

    // Format output
    if (format === 'text') {
      const textLogs = logs.map(log => {
        const timestamp = new Date(log.timestamp).toISOString();
        const level = ['DEBUG', 'INFO', 'WARN', 'ERROR', 'CRITICAL'][log.level];
        const context = log.context.agentName ? `[${log.context.agentName}]` : '';
        return `${timestamp} [${level}] ${context} ${log.message}`;
      }).join('\n');

      return new Response(textLogs, {
        headers: { 'Content-Type': 'text/plain' }
      });
    }

    return NextResponse.json({
      logs,
      meta: {
        count: logs.length,
        filtered: {
          level,
          agent,
          requestedCount: count
        }
      }
    });

  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to retrieve logs' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Logs endpoint only available in development' },
      { status: 403 }
    );
  }

  try {
    logger.clearLogs();
    return NextResponse.json({ success: true, message: 'Logs cleared' });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to clear logs' },
      { status: 500 }
    );
  }
}