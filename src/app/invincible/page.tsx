'use client';

import React, { useState } from 'react';
import { 
  Shield, 
  Download, 
  Eye, 
  Settings, 
  BarChart3, 
  FileText, 
  Target, 
  PenTool, 
  Clock, 
  DollarSign, 
  Zap, 
  Users,
  Activity,
  Sparkles,
  Search,
  Brain,
  Rocket,
  CheckCircle,
  AlertCircle,
  Star,
  Trophy,
  TrendingUp
} from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { getInvincibleAnalytics } from '@/lib/agents/invincible/invincible-agent';

export default function InvincibleAgent() {
  // State management
  const [topic, setTopic] = useState('');
  const [wordCount, setWordCount] = useState(2500);
  const [tone, setTone] = useState('professional and engaging');
  const [generatedArticle, setGeneratedArticle] = useState('');
  const [stats, setStats] = useState(null);
  const [activeTab, setActiveTab] = useState('generator');
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState({ stage: '', message: '' });
  
  // Mock team status for UI (since we're using API)
  const teamWorkflowStatus = isGenerating ? 'processing' : 'ready';

  const invincibleAnalytics = getInvincibleAnalytics();

  // Article generation function
  const generateArticle = async () => {
    if (!topic.trim()) {
      alert('Please enter a topic first');
      return;
    }

    setGeneratedArticle('');
    setStats(null);
    setIsGenerating(true);
    setProgress({ stage: 'starting', message: 'Initializing Invincible system...' });

    try {
      // Simulate progress updates
      setProgress({ stage: 'research', message: 'Aria is conducting deep research...' });
      
      setTimeout(() => {
        setProgress({ stage: 'strategy', message: 'Sage is developing content strategy...' });
      }, 2000);
      
      setTimeout(() => {
        setProgress({ stage: 'writing', message: 'Invincible is crafting your article...' });
      }, 4000);
      
      setTimeout(() => {
        setProgress({ stage: 'quality', message: 'Zara is perfecting the final piece...' });
      }, 6000);

      const response = await fetch('/api/invincible/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic,
          wordCount,
          tone,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Article generation failed');
      }

      if (result.success) {
        setGeneratedArticle(result.article);
        setStats(result.stats);
        setProgress({ stage: 'complete', message: 'Article generation complete!' });
      } else {
        console.error('Article generation failed:', result.error);
        alert('Article generation failed: ' + result.error);
        setProgress({ stage: 'error', message: result.error || 'Unknown error' });
      }
    } catch (error) {
      console.error('Error generating article:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      alert('Error generating article: ' + errorMessage);
      setProgress({ stage: 'error', message: errorMessage });
    } finally {
      setIsGenerating(false);
    }
  };

  // Export article function
  const exportArticle = (format: string) => {
    if (!generatedArticle) {
      alert('No article to export. Please generate an article first.');
      return;
    }

    let content = '';
    let filename = '';
    let mimeType = '';

    switch (format) {
      case 'md':
        content = generatedArticle;
        filename = `invincible-${topic?.replace(/\s+/g, '-').toLowerCase() || 'article'}.md`;
        mimeType = 'text/markdown';
        break;
      case 'html':
        content = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${topic || 'Invincible Article'}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; color: #333; }
        h1, h2, h3 { color: #1e40af; margin-top: 2rem; margin-bottom: 1rem; }
        h1 { border-bottom: 3px solid #3b82f6; padding-bottom: 0.5rem; }
        p { margin-bottom: 16px; }
        ul, ol { margin-bottom: 16px; padding-left: 20px; }
        blockquote { border-left: 4px solid #3b82f6; background: #f8fafc; padding: 1rem; margin: 1rem 0; }
        code { background: #f1f5f9; padding: 0.2rem 0.4rem; border-radius: 0.25rem; }
    </style>
</head>
<body>
    <div style="background: linear-gradient(45deg, #3b82f6, #06b6d4); color: white; padding: 2rem; margin-bottom: 2rem; border-radius: 0.5rem; text-align: center;">
        <h1 style="color: white; margin: 0; border: none;">Generated by Invincible Agent</h1>
        <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">Elite AI Content Creation System</p>
    </div>
    ${generatedArticle.replace(/\n/g, '<br>')}
</body>
</html>`;
        filename = `invincible-${topic?.replace(/\s+/g, '-').toLowerCase() || 'article'}.html`;
        mimeType = 'text/html';
        break;
      case 'txt':
        content = generatedArticle.replace(/[#*`]/g, '').replace(/\n+/g, '\n');
        filename = `invincible-${topic?.replace(/\s+/g, '-').toLowerCase() || 'article'}.txt`;
        mimeType = 'text/plain';
        break;
    }

    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'generator':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Input Panel */}
            <div className="space-y-6">
              <div className="bg-slate-800/50 backdrop-blur-lg border border-blue-500/30 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-4 flex items-center">
                  <Target className="w-5 h-5 mr-2 text-blue-400" />
                  Article Configuration
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">
                      Topic / Subject
                    </label>
                    <input
                      type="text"
                      value={topic}
                      onChange={(e) => setTopic(e.target.value)}
                      placeholder="Enter your article topic (e.g., 'The Future of AI in Healthcare')"
                      className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Word Count
                      </label>
                      <select
                        value={wordCount}
                        onChange={(e) => setWordCount(Number(e.target.value))}
                        className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value={1000}>Short (1,000 words)</option>
                        <option value={2000}>Medium (2,000 words)</option>
                        <option value={2500}>Long (2,500 words)</option>
                        <option value={3500}>Extended (3,500 words)</option>
                        <option value={5000}>Comprehensive (5,000 words)</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Tone & Style
                      </label>
                      <select
                        value={tone}
                        onChange={(e) => setTone(e.target.value)}
                        className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="professional and engaging">Professional & Engaging</option>
                        <option value="conversational and friendly">Conversational & Friendly</option>
                        <option value="authoritative and formal">Authoritative & Formal</option>
                        <option value="creative and inspiring">Creative & Inspiring</option>
                        <option value="technical and detailed">Technical & Detailed</option>
                        <option value="casual and approachable">Casual & Approachable</option>
                      </select>
                    </div>
                  </div>

                  <button
                    onClick={generateArticle}
                    disabled={isGenerating || !topic.trim()}
                    className="w-full py-4 px-6 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-semibold rounded-lg shadow-lg hover:from-blue-700 hover:to-cyan-700 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 transition-all duration-200 flex items-center justify-center space-x-2"
                  >
                    {isGenerating ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                        <span>Generating...</span>
                      </>
                    ) : (
                      <>
                        <Shield className="w-5 h-5" />
                        <span>Generate with Invincible</span>
                      </>
                    )}
                  </button>
                </div>
              </div>

              {/* Progress Display */}
              {(isGenerating || progress.stage) && (
                <div className="bg-slate-800/50 backdrop-blur-lg border border-blue-500/30 rounded-xl p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <Activity className="w-5 h-5 mr-2 text-green-400" />
                    Generation Progress
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-slate-300">Status:</span>
                      <span className={`font-medium ${
                        progress.stage === 'complete' ? 'text-green-400' : 
                        progress.stage === 'error' ? 'text-red-400' : 'text-blue-400'
                      }`}>
                        {progress.stage === 'complete' ? 'Complete' : 
                         progress.stage === 'error' ? 'Error' : 'In Progress'}
                      </span>
                    </div>
                    <div className="text-sm text-slate-300">
                      {progress.message}
                    </div>
                    {isGenerating && (
                      <div className="w-full bg-slate-700 rounded-full h-2">
                        <div className="bg-gradient-to-r from-blue-600 to-cyan-600 h-2 rounded-full animate-pulse"></div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Export Options */}
              {generatedArticle && (
                <div className="bg-slate-800/50 backdrop-blur-lg border border-blue-500/30 rounded-xl p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <Download className="w-5 h-5 mr-2 text-cyan-400" />
                    Export Options
                  </h3>
                  <div className="grid grid-cols-3 gap-3">
                    <button
                      onClick={() => exportArticle('md')}
                      className="py-2 px-4 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors duration-200 text-sm"
                    >
                      Markdown
                    </button>
                    <button
                      onClick={() => exportArticle('html')}
                      className="py-2 px-4 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors duration-200 text-sm"
                    >
                      HTML
                    </button>
                    <button
                      onClick={() => exportArticle('txt')}
                      className="py-2 px-4 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors duration-200 text-sm"
                    >
                      Text
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Output Panel */}
            <div className="space-y-6">
              <div className="bg-slate-800/50 backdrop-blur-lg border border-blue-500/30 rounded-xl p-6 h-[600px] overflow-hidden">
                <h3 className="text-xl font-semibold mb-4 flex items-center">
                  <FileText className="w-5 h-5 mr-2 text-cyan-400" />
                  Generated Article
                </h3>
                <div className="h-full overflow-y-auto prose prose-invert prose-slate max-w-none">
                  {generatedArticle ? (
                    <ReactMarkdown className="markdown-content">
                      {generatedArticle}
                    </ReactMarkdown>
                  ) : (
                    <div className="h-full flex items-center justify-center text-slate-400">
                      <div className="text-center">
                        <Shield className="w-16 h-16 mx-auto mb-4 opacity-50" />
                        <p>Your Invincible article will appear here</p>
                        <p className="text-sm mt-2">Enter a topic and click "Generate with Invincible" to begin</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );

      case 'agents':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {invincibleAnalytics.agents.map((agent, index) => (
              <div key={agent.name} className="bg-slate-800/50 backdrop-blur-lg border border-blue-500/30 rounded-xl p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                    index === 0 ? 'bg-red-500/20 text-red-400' :
                    index === 1 ? 'bg-purple-500/20 text-purple-400' :
                    index === 2 ? 'bg-blue-500/20 text-blue-400' :
                    'bg-green-500/20 text-green-400'
                  }`}>
                    {index === 0 ? <Search className="w-6 h-6" /> :
                     index === 1 ? <Brain className="w-6 h-6" /> :
                     index === 2 ? <Shield className="w-6 h-6" /> :
                     <CheckCircle className="w-6 h-6" />}
                  </div>
                  <div>
                    <h3 className="font-semibold text-white">{agent.name}</h3>
                    <p className="text-xs text-slate-400">{agent.role}</p>
                  </div>
                </div>
                <div className="space-y-3 text-sm">
                  <div>
                    <span className="text-slate-400">Specialization:</span>
                    <p className="text-white">{agent.specialization}</p>
                  </div>
                  <div>
                    <span className="text-slate-400">Performance:</span>
                    <p className="text-green-400 font-medium">{agent.performance}</p>
                  </div>
                  <div>
                    <span className="text-slate-400">Avg. Time:</span>
                    <p className="text-blue-400">{agent.avgExecutionTime}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        );

      case 'analytics':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div className="bg-slate-800/50 backdrop-blur-lg border border-blue-500/30 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-4 flex items-center">
                  <Trophy className="w-5 h-5 mr-2 text-yellow-400" />
                  System Capabilities
                </h3>
                <div className="space-y-4">
                  {invincibleAnalytics.capabilities.unique_features.map((feature, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <Star className="w-4 h-4 text-yellow-400" />
                      <span className="text-slate-200">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-slate-800/50 backdrop-blur-lg border border-blue-500/30 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-4 flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2 text-green-400" />
                  Competitive Advantages
                </h3>
                <div className="space-y-3">
                  {invincibleAnalytics.competitive_advantages.map((advantage, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <Zap className="w-4 h-4 text-green-400" />
                      <span className="text-slate-200">{advantage}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="space-y-6">
              <div className="bg-slate-800/50 backdrop-blur-lg border border-blue-500/30 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-4 flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2 text-blue-400" />
                  Performance Metrics
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-slate-700/50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-green-400">99.1%</div>
                    <div className="text-sm text-slate-400">Success Rate</div>
                  </div>
                  <div className="bg-slate-700/50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-blue-400">9.8/10</div>
                    <div className="text-sm text-slate-400">Quality Score</div>
                  </div>
                  <div className="bg-slate-700/50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-purple-400">7-15m</div>
                    <div className="text-sm text-slate-400">Avg. Time</div>
                  </div>
                  <div className="bg-slate-700/50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-cyan-400">$0.15-0.45</div>
                    <div className="text-sm text-slate-400">Cost Range</div>
                  </div>
                </div>
              </div>

              <div className="bg-slate-800/50 backdrop-blur-lg border border-blue-500/30 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-4 flex items-center">
                  <Settings className="w-5 h-5 mr-2 text-slate-400" />
                  Technical Specifications
                </h3>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-slate-400">Framework:</span>
                    <span className="text-white">KaibanJS</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-400">AI Model:</span>
                    <span className="text-white">Gemini 2.5 Flash Lite</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-400">Search Engine:</span>
                    <span className="text-white">Tavily Advanced</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-400">Word Count Range:</span>
                    <span className="text-white">{invincibleAnalytics.capabilities.word_count_range}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-400">Content Types:</span>
                    <span className="text-white">{invincibleAnalytics.capabilities.content_types.length} types</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 text-white">
      {/* Header */}
      <div className="border-b border-blue-500/30 bg-slate-900/80 backdrop-blur-lg sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Shield className="h-10 w-10 text-blue-400" />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-400 via-cyan-400 to-purple-400 bg-clip-text text-transparent">
                  INVINCIBLE AGENT
                </h1>
                <p className="text-sm text-slate-400">Elite Article Writing System • Leveraging Existing Gemini Service</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <div className="text-sm text-slate-400">System Status</div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-green-400">
                    {teamWorkflowStatus || 'Ready'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="max-w-7xl mx-auto px-6 py-6">
        <div className="flex space-x-1 bg-slate-800/50 backdrop-blur-lg border border-slate-700 rounded-lg p-1">
          {[
            { id: 'generator', label: 'Article Generator', icon: Rocket },
            { id: 'agents', label: 'Agent System', icon: Users },
            { id: 'analytics', label: 'Analytics', icon: BarChart3 }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg'
                    : 'text-slate-400 hover:text-white hover:bg-slate-700/50'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 pb-12">
        {renderTabContent()}
      </div>
    </div>
  );
} 