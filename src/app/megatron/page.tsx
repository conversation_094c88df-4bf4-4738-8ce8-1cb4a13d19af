'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { 
  ArrowLeft, 
  Sparkles, 
  Brain, 
  Zap, 
  Target, 
  Search, 
  TrendingUp, 
  Eye, 
  Rocket,
  Play,
  ChevronRight,
  Shield,
  Crown,
  Lightbulb,
  FileText,
  Users,
  Globe,
  Clock,
  BarChart,
  Monitor,
  Activity,
  Youtube,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import MegatronAnalysisCards from '@/components/MegatronAnalysisCards';

interface VideoAnalysis {
  videoTitle: string;
  videoUrl: string;
  analysisType: 'single' | 'multiple';
  topics: {
    id: string;
    title: string;
    summary: string[];
    timestamp?: string;
  }[];
}

export default function MegatronPage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [youtubeUrl, setYoutubeUrl] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysis, setAnalysis] = useState<VideoAnalysis | null>(null);
  const [error, setError] = useState('');

  const validateYouTubeUrl = (url: string): boolean => {
    const patterns = [
      /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]{11})/,
      /(?:https?:\/\/)?(?:www\.)?youtu\.be\/([a-zA-Z0-9_-]{11})/,
      /(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/([a-zA-Z0-9_-]{11})/,
      /(?:https?:\/\/)?(?:www\.)?youtube\.com\/v\/([a-zA-Z0-9_-]{11})/
    ];
    
    return patterns.some(pattern => pattern.test(url));
  };

  const handleUnleash = async () => {
    if (!youtubeUrl.trim()) {
      setError('Please enter a YouTube URL');
      return;
    }

    if (!validateYouTubeUrl(youtubeUrl)) {
      setError('Please enter a valid YouTube URL');
      return;
    }

    setError('');
    setIsAnalyzing(true);
    setAnalysis(null);

    try {
      const response = await fetch('/api/megatron/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          youtubeUrl: youtubeUrl.trim()
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Analysis failed');
      }

      setAnalysis(data.analysis);
    } catch (error) {
      console.error('Analysis error:', error);
      setError(error instanceof Error ? error.message : 'Failed to analyze video');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleGenerateArticle = (topic: VideoAnalysis['topics'][0]) => {
    // Navigate to Invincible page with pre-filled data
    const params = new URLSearchParams({
      topic: topic.title,
      customInstructions: `Based on the YouTube video analysis:\n\n${topic.summary.map(point => `• ${point}`).join('\n')}\n\nCreate a comprehensive article covering these key points in detail.`,
      source: 'megatron',
      videoUrl: analysis?.videoUrl || '',
      videoTitle: analysis?.videoTitle || ''
    });
    
    router.push(`/invincible?${params.toString()}`);
  };

  const handleGenerateScript = (topic: VideoAnalysis['topics'][0]) => {
    // Navigate to YouTube script generation with pre-filled data
    const params = new URLSearchParams({
      title: topic.title,
      brief: topic.summary.join('. '),
      source: 'megatron',
      videoUrl: analysis?.videoUrl || ''
    });
    
    router.push(`/youtube-scripts?${params.toString()}`);
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <Loader2 className="w-8 h-8 text-white animate-spin" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white relative overflow-hidden">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black" />
        
        {/* Matrix-style background lines */}
        <div className="absolute inset-0 opacity-10">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-px bg-gradient-to-b from-transparent via-red-400 to-transparent"
              style={{
                left: `${(i * 5)}%`,
                height: '100%'
              }}
              animate={{
                opacity: [0.1, 0.3, 0.1],
                scaleY: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 2 + Math.random() * 3,
                repeat: Infinity,
                delay: Math.random() * 2
              }}
            />
          ))}
        </div>

        {/* Floating particles */}
        <div className="absolute inset-0">
          {[...Array(50)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-red-400 rounded-full opacity-20"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -100, 0],
                opacity: [0.2, 0.8, 0.2],
              }}
              transition={{
                duration: 3 + Math.random() * 4,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
      </div>

      {/* Header */}
      <header className="relative z-10 border-b border-white/10 backdrop-blur-md">
        <div className="max-w-6xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Link 
              href="/dashboard"
              className="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>Back to Dashboard</span>
            </Link>
            
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="absolute inset-0 rounded-xl blur-lg opacity-70 bg-gradient-to-r from-red-600 to-orange-600" />
                <div className="relative bg-black rounded-xl p-2.5 border border-white/20">
                  <Zap className="w-6 h-6 text-white" />
                </div>
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">Megatron</h1>
                <p className="text-sm text-gray-400">YouTube Video Analyzer</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 max-w-6xl mx-auto px-6 py-12">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-8">
            <Youtube className="w-4 h-4 text-red-400" />
            <span className="text-sm text-gray-200">Video Analysis</span>
            <Brain className="w-4 h-4 text-purple-400" />
            <span className="text-sm text-gray-200">AI-Powered</span>
            <Zap className="w-4 h-4 text-orange-400" />
          </div>

          <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-red-400 via-orange-400 to-yellow-400 bg-clip-text text-transparent">
            Megatron
          </h1>

          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Transform YouTube videos into actionable content. Extract topics, analyze structure,
            and generate articles or scripts with AI precision.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-12">
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-orange-500 rounded-lg flex items-center justify-center mb-4 mx-auto">
                <Youtube className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-white mb-2">Caption Extraction</h3>
              <p className="text-sm text-gray-400">
                Advanced caption extraction from any YouTube video with multi-language support
              </p>
            </div>

            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mb-4 mx-auto">
                <Brain className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-white mb-2">Smart Analysis</h3>
              <p className="text-sm text-gray-400">
                AI-powered topic identification and content structure analysis
              </p>
            </div>

            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-cyan-500 rounded-lg flex items-center justify-center mb-4 mx-auto">
                <Rocket className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-white mb-2">Content Generation</h3>
              <p className="text-sm text-gray-400">
                Generate articles and scripts directly from analyzed video content
              </p>
            </div>
          </div>
        </motion.div>

        {/* URL Input Section */}
        {!analysis && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="max-w-2xl mx-auto mb-16"
          >
            <div className="bg-white/5 backdrop-blur-md border border-white/20 rounded-2xl p-8">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-white mb-2">Analyze YouTube Video</h2>
                <p className="text-gray-400">
                  Paste any YouTube URL to extract topics and generate content
                </p>
              </div>

              <div className="space-y-4">
                <div className="relative">
                  <input
                    type="url"
                    value={youtubeUrl}
                    onChange={(e) => setYoutubeUrl(e.target.value)}
                    placeholder="https://www.youtube.com/watch?v=..."
                    className="w-full px-4 py-3 bg-black/50 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    disabled={isAnalyzing}
                  />
                  <Youtube className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                </div>

                {error && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex items-center space-x-2 text-red-400 text-sm"
                  >
                    <AlertCircle className="w-4 h-4" />
                    <span>{error}</span>
                  </motion.div>
                )}

                <Button
                  onClick={handleUnleash}
                  disabled={isAnalyzing || !youtubeUrl.trim()}
                  className="w-full bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isAnalyzing ? (
                    <div className="flex items-center space-x-2">
                      <Loader2 className="w-5 h-5 animate-spin" />
                      <span>Analyzing...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <Zap className="w-5 h-5" />
                      <span>Unleash</span>
                    </div>
                  )}
                </Button>
              </div>
            </div>
          </motion.div>
        )}

        {/* Analysis Results */}
        <AnimatePresence>
          {analysis && (
            <MegatronAnalysisCards
              analysis={analysis}
              onGenerateArticle={handleGenerateArticle}
              onGenerateScript={handleGenerateScript}
              onReset={() => {
                setAnalysis(null);
                setYoutubeUrl('');
                setError('');
              }}
            />
          )}
        </AnimatePresence>
      </main>
    </div>
  );
}
