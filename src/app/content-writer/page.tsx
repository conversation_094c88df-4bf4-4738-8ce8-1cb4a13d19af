'use client';

import React, { useState } from 'react';
import { 
  Play, 
  Download, 
  Eye, 
  Settings, 
  BarChart3, 
  FileText, 
  Target, 
  PenTool, 
  Clock, 
  DollarSign, 
  Zap, 
  Users,
  Activity,
  Sparkles
} from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { contentTeam, executeContentWorkflow, getTeamAnalytics } from '@/lib/agents/content-team/contentTeam';

export default function ContentWriter() {
  // State management
  const [topic, setTopic] = useState('');
  const [wordCount, setWordCount] = useState(2000);
  const [tone, setTone] = useState('professional');
  const [contentType, setContentType] = useState('blog-post');
  const [generatedContent, setGeneratedContent] = useState('');
  const [stats, setStats] = useState(null);
  const [activeTab, setActiveTab] = useState('generator');
  
  // Team store integration
  const useTeamStore = contentTeam.useStore();
  const {
    agents,
    tasks,
    teamWorkflowStatus
  } = useTeamStore(state => ({
    agents: state.agents,
    tasks: state.tasks,
    teamWorkflowStatus: state.teamWorkflowStatus
  }));

  const teamAnalytics = getTeamAnalytics();

  // Content generation function
  const generateContent = async () => {
    if (!topic.trim()) {
      alert('Please enter a topic first');
      return;
    }

    setGeneratedContent('');
    setStats(null);

    try {
      const result = await executeContentWorkflow({
        topic,
        wordCount,
        tone,
        contentType
      });

      if (result.success) {
        setGeneratedContent(result.content);
        setStats(result.metadata);
      } else {
        console.error('Content generation failed:', result.error);
        alert('Content generation failed: ' + result.error);
      }
    } catch (error) {
      console.error('Error generating content:', error);
      alert('Error generating content: ' + error.message);
    }
  };

  // Export content function
  const exportContent = (format) => {
    if (!generatedContent) {
      alert('No content to export. Please generate content first.');
      return;
    }

    let content = '';
    let filename = '';
    let mimeType = '';

    switch (format) {
      case 'md':
        content = generatedContent;
        filename = `${topic?.replace(/\s+/g, '-').toLowerCase() || 'content'}.md`;
        mimeType = 'text/markdown';
        break;
      case 'html':
        content = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${topic || 'Generated Content'}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1, h2, h3 { color: #333; }
        p { margin-bottom: 16px; }
        ul, ol { margin-bottom: 16px; padding-left: 20px; }
    </style>
</head>
<body>
    ${generatedContent.replace(/\n/g, '<br>')}
</body>
</html>`;
        filename = `${topic?.replace(/\s+/g, '-').toLowerCase() || 'content'}.html`;
        mimeType = 'text/html';
        break;
      case 'txt':
        content = generatedContent.replace(/[#*`]/g, '').replace(/\n+/g, '\n');
        filename = `${topic?.replace(/\s+/g, '-').toLowerCase() || 'content'}.txt`;
        mimeType = 'text/plain';
        break;
    }

    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 text-white">
      {/* Header */}
      <div className="border-b border-blue-500/30 bg-slate-900/80 backdrop-blur-lg sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Sparkles className="h-10 w-10 text-blue-400" />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
                  AI CONTENT WRITER
                </h1>
                <p className="text-sm text-slate-400">Powered by KaibanJS & Kimi K2</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <div className="text-sm text-slate-400">Status</div>
                <div className="text-sm font-medium text-green-400">{teamWorkflowStatus || 'READY'}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Tab Navigation */}
        <div className="flex space-x-1 bg-slate-800/50 p-2 rounded-xl mb-8 backdrop-blur-sm">
          {[
            { id: 'generator', label: 'Content Generator', icon: Zap },
            { id: 'preview', label: 'Content Preview', icon: Eye },
            { id: 'agents', label: 'AI Agents', icon: Users },
            { id: 'analytics', label: 'Analytics', icon: BarChart3 }
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id)}
              className={`flex items-center space-x-2 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                activeTab === id
                  ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg'
                  : 'text-slate-400 hover:text-white hover:bg-slate-700/50'
              }`}
            >
              <Icon className="h-4 w-4" />
              <span>{label}</span>
            </button>
          ))}
        </div>

        {/* Content Generator Tab */}
        {activeTab === 'generator' && (
          <div className="space-y-8">
            <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm">
              <div className="p-6 border-b border-slate-700/50">
                <div className="flex items-center space-x-3 mb-4">
                  <Zap className="h-6 w-6 text-blue-400" />
                  <h2 className="text-xl font-bold">Content Generation</h2>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        <Target className="h-4 w-4 inline mr-2" />
                        Topic *
                      </label>
                      <input 
                        type="text"
                        value={topic}
                        onChange={(e) => setTopic(e.target.value)}
                        placeholder="Enter your content topic..."
                        className="w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-white placeholder-slate-400"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        <FileText className="h-4 w-4 inline mr-2" />
                        Content Type
                      </label>
                      <select 
                        value={contentType}
                        onChange={(e) => setContentType(e.target.value)}
                        className="w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-white"
                      >
                        <option value="blog-post">Blog Post</option>
                        <option value="article">Article</option>
                        <option value="landing-page">Landing Page</option>
                        <option value="social-media">Social Media</option>
                        <option value="email-campaign">Email Campaign</option>
                      </select>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        <PenTool className="h-4 w-4 inline mr-2" />
                        Word Count: {wordCount}
                      </label>
                      <input 
                        type="range"
                        min="500"
                        max="5000"
                        step="100"
                        value={wordCount}
                        onChange={(e) => setWordCount(parseInt(e.target.value))}
                        className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider"
                      />
                      <div className="flex justify-between text-xs text-slate-400 mt-1">
                        <span>500</span>
                        <span>5000</span>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        <Settings className="h-4 w-4 inline mr-2" />
                        Tone
                      </label>
                      <select 
                        value={tone}
                        onChange={(e) => setTone(e.target.value)}
                        className="w-full px-4 py-3 bg-slate-800/50 border border-slate-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-white"
                      >
                        <option value="professional">Professional</option>
                        <option value="casual">Casual</option>
                        <option value="friendly">Friendly</option>
                        <option value="authoritative">Authoritative</option>
                        <option value="conversational">Conversational</option>
                      </select>
                    </div>
                  </div>
                </div>
                <div className="mt-6 flex justify-center">
                  <button 
                    onClick={generateContent}
                    disabled={!topic.trim() || teamWorkflowStatus === 'RUNNING'}
                    className="px-8 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-medium rounded-lg hover:from-blue-600 hover:to-cyan-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center space-x-2"
                  >
                    <Play className="h-5 w-5" />
                    <span>{teamWorkflowStatus === 'RUNNING' ? 'Generating...' : 'Generate Content'}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Content Preview Tab */}
        {activeTab === 'preview' && (
          <div className="space-y-8">
            <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm">
              <div className="p-6 border-b border-slate-700/50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Eye className="h-6 w-6 text-blue-400" />
                    <h2 className="text-xl font-bold">Generated Content</h2>
                  </div>
                  {generatedContent && (
                    <div className="flex space-x-2">
                      <button
                        onClick={() => exportContent('md')}
                        className="px-4 py-2 bg-slate-700 hover:bg-slate-600 border border-slate-600 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                      >
                        <Download className="h-4 w-4" />
                        <span>MD</span>
                      </button>
                      <button
                        onClick={() => exportContent('html')}
                        className="px-4 py-2 bg-slate-700 hover:bg-slate-600 border border-slate-600 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                      >
                        <Download className="h-4 w-4" />
                        <span>HTML</span>
                      </button>
                      <button
                        onClick={() => exportContent('txt')}
                        className="px-4 py-2 bg-slate-700 hover:bg-slate-600 border border-slate-600 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                      >
                        <Download className="h-4 w-4" />
                        <span>TXT</span>
                      </button>
                    </div>
                  )}
                </div>
              </div>

              <div className="p-6">
                <div className="bg-slate-900/50 rounded-xl border border-slate-700/50 p-6 min-h-[400px]">
                  {generatedContent ? (
                    <div className="prose prose-invert prose-blue max-w-none">
                      <ReactMarkdown>{generatedContent}</ReactMarkdown>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full text-slate-500">
                      <div className="text-center">
                        <Eye className="h-16 w-16 mx-auto mb-4 opacity-50" />
                        <p className="text-lg">Content will appear here after generation</p>
                        <p className="text-sm mt-2">Configure your settings and generate content to see results</p>
                      </div>
                    </div>
                  )}
                </div>

                {stats && (
                  <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-slate-800/50 rounded-lg p-4">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-5 w-5 text-blue-400" />
                        <span className="text-sm font-medium">Duration</span>
                      </div>
                      <p className="text-lg font-bold text-white mt-1">{stats.executionTime || 'N/A'}</p>
                    </div>
                    <div className="bg-slate-800/50 rounded-lg p-4">
                      <div className="flex items-center space-x-2">
                        <Activity className="h-5 w-5 text-green-400" />
                        <span className="text-sm font-medium">Tokens</span>
                      </div>
                      <p className="text-lg font-bold text-white mt-1">{stats.tokenCount || 'N/A'}</p>
                    </div>
                    <div className="bg-slate-800/50 rounded-lg p-4">
                      <div className="flex items-center space-x-2">
                        <DollarSign className="h-5 w-5 text-yellow-400" />
                        <span className="text-sm font-medium">Cost</span>
                      </div>
                      <p className="text-lg font-bold text-white mt-1">${stats.cost?.toFixed(4) || 'N/A'}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* AI Agents Tab */}
        {activeTab === 'agents' && (
          <div className="space-y-8">
            <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm">
              <div className="p-6 border-b border-slate-700/50">
                <div className="flex items-center space-x-3 mb-4">
                  <Users className="h-6 w-6 text-blue-400" />
                  <h2 className="text-xl font-bold">AI Agent Team</h2>
                </div>
              </div>

              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {teamAnalytics.agents.map((agent, index) => (
                    <div key={index} className="bg-slate-800/50 rounded-xl border border-slate-700/50 p-6">
                      <div className="flex items-center space-x-3 mb-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                          <span className="text-white font-bold text-lg">{agent.name.charAt(0)}</span>
                        </div>
                        <div>
                          <h3 className="font-bold text-white">{agent.name}</h3>
                          <p className="text-sm text-slate-400">{agent.role}</p>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div>
                          <p className="text-sm text-slate-400">Specialization</p>
                          <p className="text-sm text-white">{agent.specialization}</p>
                        </div>
                        <div>
                          <p className="text-sm text-slate-400">Tools</p>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {agent.tools.map((tool, toolIndex) => (
                              <span key={toolIndex} className="px-2 py-1 bg-blue-500/20 text-blue-300 text-xs rounded">
                                {tool}
                              </span>
                            ))}
                          </div>
                        </div>
                        <div>
                          <p className="text-sm text-slate-400">Performance</p>
                          <p className="text-sm text-green-400">{agent.performance}</p>
                        </div>
                        <div>
                          <p className="text-sm text-slate-400">Avg. Execution Time</p>
                          <p className="text-sm text-white">{agent.avgExecutionTime}</p>
                        </div>
                      </div>

                      {agents && agents.find(a => a.name === agent.name) && (
                        <div className="mt-4 pt-4 border-t border-slate-700/50">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-slate-400">Status</span>
                            <span className={`text-sm px-2 py-1 rounded ${
                              agents.find(a => a.name === agent.name)?.status === 'WORKING'
                                ? 'bg-green-500/20 text-green-400'
                                : 'bg-slate-500/20 text-slate-400'
                            }`}>
                              {agents.find(a => a.name === agent.name)?.status || 'IDLE'}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && (
          <div className="space-y-8">
            <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl border border-blue-500/20 backdrop-blur-sm">
              <div className="p-6 border-b border-slate-700/50">
                <div className="flex items-center space-x-3 mb-4">
                  <BarChart3 className="h-6 w-6 text-blue-400" />
                  <h2 className="text-xl font-bold">Team Analytics</h2>
                </div>
              </div>

              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* Capabilities */}
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-4">Capabilities</h3>
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm text-slate-400 mb-2">Content Types</p>
                        <div className="flex flex-wrap gap-2">
                          {teamAnalytics.capabilities.content_types.map((type, index) => (
                            <span key={index} className="px-3 py-1 bg-blue-500/20 text-blue-300 text-sm rounded-full">
                              {type}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div>
                        <p className="text-sm text-slate-400 mb-2">Word Count Range</p>
                        <p className="text-white">{teamAnalytics.capabilities.word_count_range}</p>
                      </div>
                      <div>
                        <p className="text-sm text-slate-400 mb-2">SEO Optimization</p>
                        <p className="text-white">{teamAnalytics.capabilities.seo_optimization}</p>
                      </div>
                      <div>
                        <p className="text-sm text-slate-400 mb-2">Fact Checking</p>
                        <p className="text-white">{teamAnalytics.capabilities.fact_checking}</p>
                      </div>
                      <div>
                        <p className="text-sm text-slate-400 mb-2">Brand Voice</p>
                        <p className="text-white">{teamAnalytics.capabilities.brand_voice}</p>
                      </div>
                    </div>
                  </div>

                  {/* Workflow Stats */}
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-4">Workflow Performance</h3>
                    <div className="space-y-4">
                      {teamAnalytics.workflows.map((workflow, index) => (
                        <div key={index} className="bg-slate-800/50 rounded-lg p-4">
                          <h4 className="font-medium text-white mb-2">{workflow.name}</h4>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <p className="text-slate-400">Agents</p>
                              <p className="text-white">{workflow.agents}</p>
                            </div>
                            <div>
                              <p className="text-slate-400">Tasks</p>
                              <p className="text-white">{workflow.tasks}</p>
                            </div>
                            <div>
                              <p className="text-slate-400">Est. Time</p>
                              <p className="text-white">{workflow.estimatedTime}</p>
                            </div>
                            <div>
                              <p className="text-slate-400">Success Rate</p>
                              <p className="text-green-400">{workflow.success_rate}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
