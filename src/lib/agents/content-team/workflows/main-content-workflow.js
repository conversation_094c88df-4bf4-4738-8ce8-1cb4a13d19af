/**
 * Main Content Creation Workflow - KaibanJS + OpenRouter <PERSON><PERSON>
 * Sequential 5-agent workflow for professional content generation
 */
import { Team } from 'kaibanjs';
import { researchAgent, conductTopicResearch, gatherSupportingData, factCheck } from '../agents/research-agent.js';
import { seoStrategistAgent, developKeywordStrategy, analyzeSerpCompetitors, createSeoOptimizationPlan } from '../agents/seo-strategist.js';
import { contentArchitectAgent, createContentOutline, structureInformation, planContentFlow } from '../agents/content-architect.js';
import { contentCreatorAgent, writeMainContent, optimizeForEngagement, addVisualElements } from '../agents/content-creator.js';
import { qualityEditorAgent, reviewAndOptimize, finalProofread, validateSeoCompliance } from '../agents/quality-editor.js';

/**
 * Main Content Writing Team
 * 5-agent collaborative system for generating high-quality, SEO-optimized content
 */
export class ContentWritingTeam {
  constructor() {
    this.team = new Team({
      name: 'Advanced Content Writing Team',
      agents: [
        researchAgent,
        seoStrategistAgent, 
        contentArchitectAgent,
        contentCreatorAgent,
        qualityEditorAgent
      ],
      tasks: [], // Will be set dynamically based on workflow type
      verbose: 2,
      env: {
        OPENROUTER_API_KEY: process.env.NEXT_PUBLIC_OPENROUTER_API_KEY,
        TAVILY_API_KEY: process.env.NEXT_PUBLIC_TAVILY_API_KEY
      }
    });
  }

  /**
   * Execute the main content creation workflow
   * @param {Object} params - Workflow parameters
   * @param {string} params.topic - Main content topic
   * @param {string[]} params.targetKeywords - Target SEO keywords
   * @param {string} params.contentType - Type of content (blog-post, article, etc.)
   * @param {number} params.wordCount - Target word count
   * @param {string} params.targetAudience - Target audience description
   * @param {string} params.brandVoice - Brand voice/tone guidelines
   * @param {Function} params.onProgress - Progress callback function
   */
  async executeMainWorkflow(params) {
    const {
      topic,
      targetKeywords = [],
      contentType = 'blog-post',
      wordCount = 2000,
      targetAudience = 'general audience',
      brandVoice = 'professional',
      onProgress
    } = params;

    try {
      console.log('🚀 Starting Content Creation Workflow...');
      
      // Configure workflow tasks with parameters
      const workflowTasks = this.createWorkflowTasks({
        topic,
        targetKeywords,
        contentType,
        wordCount,
        targetAudience,
        brandVoice
      });

      this.team.tasks = workflowTasks;

      // Execute the workflow with progress tracking
      const results = await this.team.start({
        onTaskComplete: (task, result) => {
          console.log(`✅ Completed: ${task.description.split('\n')[0]}`);
          if (onProgress) {
            onProgress({
              taskName: task.description.split('\n')[0],
              agent: task.agent.name,
              result,
              progress: this.calculateProgress(task)
            });
          }
        },
        onTaskError: (task, error) => {
          console.error(`❌ Error in ${task.agent.name}:`, error);
          if (onProgress) {
            onProgress({
              taskName: task.description.split('\n')[0],
              agent: task.agent.name,
              error: error.message,
              progress: this.calculateProgress(task)
            });
          }
        }
      });

      console.log('🎉 Content Creation Workflow Completed!');
      return {
        status: 'success',
        results,
        workflow: 'main-content-creation',
        metadata: {
          topic,
          contentType,
          wordCount,
          targetKeywords,
          completedAt: new Date().toISOString(),
          totalTasks: workflowTasks.length
        }
      };

    } catch (error) {
      console.error('💥 Workflow Error:', error);
      return {
        status: 'error',
        error: error.message,
        workflow: 'main-content-creation'
      };
    }
  }

  /**
   * Create the sequential workflow tasks
   */
  createWorkflowTasks(params) {
    const { topic, targetKeywords, contentType, wordCount, targetAudience, brandVoice } = params;
    
    return [
      // Phase 1: Research & Data Collection
      {
        ...conductTopicResearch,
        description: conductTopicResearch.description.replace('{topic}', topic),
        context: {
          topic,
          targetKeywords,
          contentType,
          phase: 'research'
        }
      },

      {
        ...gatherSupportingData,
        description: gatherSupportingData.description.replace('{topic}', topic),
        context: {
          topic,
          previousResults: '{{conductTopicResearch.output}}',
          phase: 'data-gathering'
        }
      },

      // Phase 2: SEO Strategy Development
      {
        ...developKeywordStrategy,
        description: developKeywordStrategy.description.replace('{topic}', topic),
        context: {
          topic,
          targetKeywords,
          researchFindings: '{{gatherSupportingData.output}}',
          phase: 'seo-strategy'
        }
      },

      {
        ...analyzeSerpCompetitors,
        description: analyzeSerpCompetitors.description.replace('{topic}', topic),
        context: {
          topic,
          keywordStrategy: '{{developKeywordStrategy.output}}',
          phase: 'competitor-analysis'
        }
      },

      {
        ...createSeoOptimizationPlan,
        description: createSeoOptimizationPlan.description.replace('{topic}', topic),
        context: {
          topic,
          keywordStrategy: '{{developKeywordStrategy.output}}',
          competitorAnalysis: '{{analyzeSerpCompetitors.output}}',
          contentType,
          phase: 'seo-planning'
        }
      },

      // Phase 3: Content Architecture
      {
        ...createContentOutline,
        description: createContentOutline.description
          .replace('{topic}', topic)
          .replace('{word_count}', wordCount.toString())
          .replace('{content_type}', contentType),
        context: {
          topic,
          wordCount,
          contentType,
          researchData: '{{gatherSupportingData.output}}',
          seoStrategy: '{{createSeoOptimizationPlan.output}}',
          targetAudience,
          phase: 'architecture'
        }
      },

      {
        ...structureInformation,
        description: structureInformation.description.replace('{topic}', topic),
        context: {
          topic,
          contentOutline: '{{createContentOutline.output}}',
          researchFindings: '{{conductTopicResearch.output}}',
          phase: 'structure-planning'
        }
      },

      {
        ...planContentFlow,
        description: planContentFlow.description
          .replace('{topic}', topic)
          .replace('{target_audience}', targetAudience),
        context: {
          topic,
          targetAudience,
          contentStructure: '{{structureInformation.output}}',
          seoRequirements: '{{createSeoOptimizationPlan.output}}',
          phase: 'flow-planning'
        }
      },

      // Phase 4: Content Creation (Kimi K2)
      {
        ...writeMainContent,
        description: writeMainContent.description
          .replace('{topic}', topic)
          .replace('{word_count}', wordCount.toString())
          .replace('{brand_voice}', brandVoice),
        context: {
          topic,
          wordCount,
          brandVoice,
          targetAudience,
          contentPlan: '{{planContentFlow.output}}',
          researchData: '{{conductTopicResearch.output}}',
          seoGuidelines: '{{createSeoOptimizationPlan.output}}',
          phase: 'content-creation'
        }
      },

      {
        ...optimizeForEngagement,
        description: optimizeForEngagement.description.replace('{topic}', topic),
        context: {
          topic,
          contentDraft: '{{writeMainContent.output}}',
          targetAudience,
          brandVoice,
          phase: 'engagement-optimization'
        }
      },

      // Phase 5: Quality Review & Final Optimization
      {
        ...reviewAndOptimize,
        description: reviewAndOptimize.description.replace('{topic}', topic),
        context: {
          topic,
          contentDraft: '{{optimizeForEngagement.output}}',
          seoRequirements: '{{createSeoOptimizationPlan.output}}',
          originalResearch: '{{conductTopicResearch.output}}',
          phase: 'quality-review'
        }
      },

      {
        ...validateSeoCompliance,
        description: validateSeoCompliance.description.replace('{topic}', topic),
        context: {
          topic,
          finalContent: '{{reviewAndOptimize.output}}',
          seoStrategy: '{{createSeoOptimizationPlan.output}}',
          keywordStrategy: '{{developKeywordStrategy.output}}',
          phase: 'seo-validation'
        }
      },

      {
        ...finalProofread,
        description: finalProofread.description.replace('{topic}', topic),
        context: {
          topic,
          optimizedContent: '{{validateSeoCompliance.output}}',
          brandVoice,
          targetAudience,
          phase: 'final-proofread'
        }
      },

      // Final Phase: Fact-checking and Validation
      {
        ...factCheck,
        description: factCheck.description.replace('{topic}', topic),
        context: {
          topic,
          finalContent: '{{finalProofread.output}}',
          originalResearch: '{{conductTopicResearch.output}}',
          phase: 'fact-checking'
        }
      }
    ];
  }

  /**
   * Calculate progress based on completed tasks
   */
  calculateProgress(currentTask) {
    const phases = [
      'research', 'data-gathering', 'seo-strategy', 'competitor-analysis', 
      'seo-planning', 'architecture', 'structure-planning', 'flow-planning',
      'content-creation', 'engagement-optimization', 'quality-review', 
      'seo-validation', 'final-proofread', 'fact-checking'
    ];
    
    const currentPhase = currentTask.context?.phase;
    const phaseIndex = phases.indexOf(currentPhase);
    
    if (phaseIndex === -1) return 0;
    return Math.round(((phaseIndex + 1) / phases.length) * 100);
  }

  /**
   * Get workflow analytics and configuration
   */
  getAnalytics() {
    return {
      team: this.team,
      agents: this.team.agents.map(agent => ({
        name: agent.name,
        role: agent.role,
        goal: agent.goal,
        tools: agent.tools?.map(tool => tool.constructor.name) || [],
        llmConfig: agent.llmConfig
      })),
      workflows: [
        {
          name: 'Main Content Creation',
          description: 'Complete 5-agent content creation workflow',
          phases: 5,
          tasks: 14,
          estimatedTime: '4-6 minutes',
          costEstimate: '$0.40-0.80'
        }
      ],
      capabilities: {
        contentTypes: ['blog-post', 'article', 'landing-page', 'product-description', 'social-media'],
        wordCountRange: '500-5000 words',
        languages: ['English'],
        seoOptimization: true,
        factChecking: true,
        brandVoiceAdaptation: true,
        realTimeProgress: true
      }
    };
  }

  /**
   * Validate team configuration
   */
  validateConfiguration() {
    const issues = [];
    
    // Check API keys
    if (!process.env.NEXT_PUBLIC_OPENROUTER_API_KEY) {
      issues.push('Missing OPENROUTER_API_KEY environment variable');
    }
    
    if (!process.env.NEXT_PUBLIC_TAVILY_API_KEY) {
      issues.push('Missing TAVILY_API_KEY environment variable');
    }
    
    // Check agent configuration
    if (!this.team.agents || this.team.agents.length !== 5) {
      issues.push('Team should have exactly 5 agents');
    }
    
    // Check agent tools
    this.team.agents.forEach(agent => {
      if (!agent.tools || agent.tools.length === 0) {
        issues.push(`${agent.name} is missing required tools`);
      }
    });
    
    return {
      isValid: issues.length === 0,
      issues
    };
  }
}

// Export the main workflow functions
export async function executeMainContentWorkflow(params) {
  const team = new ContentWritingTeam();
  return await team.executeMainWorkflow(params);
}

export function getTeamAnalytics() {
  const team = new ContentWritingTeam();
  return team.getAnalytics();
}

export function validateTeamConfiguration() {
  const team = new ContentWritingTeam();
  return team.validateConfiguration();
}

export default ContentWritingTeam;