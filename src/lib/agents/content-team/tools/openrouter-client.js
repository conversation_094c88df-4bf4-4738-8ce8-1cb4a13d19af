/**
 * OpenRouter Client for Kimi K2 Model Integration
 * Provides a unified interface for content generation using Kimi K2 via OpenRouter API
 */

import { OpenAI } from 'openai';

export class OpenRouterClient {
  constructor(apiKey) {
    this.client = new OpenAI({
      apiKey: apiKey || process.env.NEXT_PUBLIC_OPENROUTER_API_KEY,
      baseURL: "https://openrouter.ai/api/v1/",
      defaultHeaders: {
        "HTTP-Referer": "https://yourapp.com",
        "X-Title": "Content Writing Agent"
      }
    });
    
    this.model = "moonshotai/kimi-k2";
    this.maxTokens = 8000; // Conservative limit for output
    this.temperature = 0.7; // Balanced creativity and consistency
  }

  /**
   * Generate content using Kimi K2 model
   * @param {string} prompt - The content generation prompt
   * @param {Object} options - Additional options for generation
   * @returns {Promise<Object>} Generated content with metadata
   */
  async generateContent(prompt, options = {}) {
    try {
      const startTime = Date.now();
      
      const completion = await this.client.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: "system",
            content: options.systemPrompt || "You are an expert content writer focused on creating high-quality, engaging, and SEO-optimized content."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: options.maxTokens || this.maxTokens,
        temperature: options.temperature || this.temperature,
        stream: false
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      return {
        content: completion.choices[0].message.content,
        usage: {
          inputTokens: completion.usage.prompt_tokens,
          outputTokens: completion.usage.completion_tokens,
          totalTokens: completion.usage.total_tokens
        },
        model: this.model,
        duration: duration,
        cost: this.calculateCost(completion.usage)
      };

    } catch (error) {
      console.error('OpenRouter API Error:', error);
      throw new Error(`Content generation failed: ${error.message}`);
    }
  }

  /**
   * Calculate estimated cost based on Kimi K2 pricing
   * @param {Object} usage - Token usage from API response
   * @returns {number} Estimated cost in USD
   */
  calculateCost(usage) {
    // Kimi K2 pricing: $0.15/$0.60 input (cache hit/miss), $2.50 output per 1M tokens
    // Using cache miss rate for conservative estimates
    const inputCostPer1M = 0.60; // $0.60 per 1M input tokens (cache miss)
    const outputCostPer1M = 2.50; // $2.50 per 1M output tokens
    
    const inputCost = (usage.prompt_tokens / 1000000) * inputCostPer1M;
    const outputCost = (usage.completion_tokens / 1000000) * outputCostPer1M;
    
    return inputCost + outputCost;
  }

  /**
   * Stream content generation (for real-time UI updates)
   * @param {string} prompt - The content generation prompt
   * @param {Function} onChunk - Callback for each content chunk
   * @param {Object} options - Additional options
   */
  async streamContent(prompt, onChunk, options = {}) {
    try {
      const stream = await this.client.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: "system", 
            content: options.systemPrompt || "You are an expert content writer focused on creating high-quality, engaging, and SEO-optimized content."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: options.maxTokens || this.maxTokens,
        temperature: options.temperature || this.temperature,
        stream: true
      });

      let fullContent = '';
      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content || '';
        if (content) {
          fullContent += content;
          onChunk(content, fullContent);
        }
      }

      return fullContent;

    } catch (error) {
      console.error('OpenRouter Streaming Error:', error);
      throw new Error(`Content streaming failed: ${error.message}`);
    }
  }

  /**
   * Validate API key and connection
   * @returns {Promise<boolean>} Connection status
   */
  async validateConnection() {
    try {
      const response = await this.generateContent(
        "Test connection", 
        { maxTokens: 10, systemPrompt: "Respond with 'OK'" }
      );
      return response.content.includes('OK');
    } catch (error) {
      console.error('Connection validation failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const openRouterClient = new OpenRouterClient();

// Tool configuration for KaibanJS agents
export const kimiK2Tool = {
  name: 'kimi_k2_writer',
  description: 'Advanced content writing using Kimi K2 model via OpenRouter API. Excellent for creative writing, long-form content, and maintaining context.',
  
  async call(prompt, options = {}) {
    return await openRouterClient.generateContent(prompt, options);
  },
  
  // KaibanJS tool interface
  func: async (prompt, options = {}) => {
    const result = await openRouterClient.generateContent(prompt, options);
    return result.content;
  }
};