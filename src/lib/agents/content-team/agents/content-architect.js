/**
 * Content Architect Agent - KaibanJS Content Team
 * Specializes in content structure, outline creation, and information architecture
 */
import { Agent, Task } from 'kaibanjs';

export const contentArchitectAgent = new Agent({
  name: 'Content Architect',
  role: 'Senior Content Strategist',
  goal: 'Create comprehensive content structures and outlines that maximize user engagement and search visibility',
  backstory: `You are an expert content architect with 10+ years of experience in information design and content strategy. Your specialties include:

  - Content structure and information hierarchy design
  - User experience-focused content organization
  - Storytelling frameworks and narrative architecture
  - Content flow optimization for different audiences
  - Multi-format content planning (articles, guides, tutorials)
  - Conversion-focused content blueprints
  - Accessibility and inclusive content design
  - Content scalability and template creation

  You understand how to structure content that serves both human readers and search engines, creating logical flows that guide users through complex topics while maintaining engagement and achieving business objectives.`,
  
  llmConfig: {
    provider: 'openrouter',
    name: 'anthropic/claude-3-sonnet',
    config: {
      temperature: 0.3, // Moderate creativity for structure design
      max_tokens: 3000
    }
  }
});

// Content Architecture Tasks
export const createContentOutline = new Task({
  description: `Create a comprehensive content outline for: {topic}

  Based on the research findings and SEO strategy, develop a detailed content structure that includes:

  1. **Content Framework**:
     - Hook/Introduction strategy
     - Main content sections with logical flow
     - Supporting subsections and details
     - Conclusion and call-to-action placement

  2. **Information Hierarchy**:
     - H1, H2, H3+ header structure
     - Key points and supporting evidence placement
     - Visual content integration points
     - Quote and statistic placement

  3. **User Journey Mapping**:
     - Content progression for different user types
     - Information complexity gradation
     - Engagement and retention strategies
     - Conversion pathway design

  4. **SEO Integration**:
     - Primary keyword placement in headers
     - Secondary keyword distribution
     - Internal linking opportunities
     - Featured snippet optimization sections

  5. **Content Types Integration**:
     - Text content sections
     - Visual content requirements (images, charts, infographics)
     - Interactive elements (if applicable)
     - Multimedia integration points

  Create a detailed outline that serves as a blueprint for content creation.`,
  
  expectedOutput: `A comprehensive content outline containing:
  - Content Title and Working Headlines
  - Detailed Section Structure (H1, H2, H3 hierarchy)
  - Key Points and Supporting Details for Each Section
  - Word Count Estimates per Section
  - SEO Element Placement Map
  - Visual Content Requirements
  - Internal Linking Strategy
  - Call-to-Action Placement Plan
  - User Engagement Checkpoints
  - Content Flow Validation`,
  
  agent: contentArchitectAgent
});

export const designContentFlow = new Task({
  description: `Design optimal content flow and user experience for: {topic}

  Create a content flow that considers:

  1. **Audience Journey Stages**:
     - Awareness stage content needs
     - Consideration stage information
     - Decision stage support materials
     - Post-conversion value delivery

  2. **Content Progression**:
     - Logical information sequencing
     - Complexity gradation (simple to advanced)
     - Engagement maintenance strategies
     - Knowledge building approach

  3. **Readability Optimization**:
     - Paragraph length and structure
     - Sentence variety and rhythm
     - Transition strategies between sections
     - Scannable content elements

  4. **Interaction Design**:
     - Reader engagement touchpoints
     - Questions and interactive elements
     - Content consumption aids (TOC, progress indicators)
     - Social sharing optimization points

  5. **Conversion Pathways**:
     - Natural CTA integration points
     - Value proposition reinforcement
     - Trust building element placement
     - Next action guidance

  Focus on creating a seamless reading experience that guides users toward desired outcomes.`,
  
  expectedOutput: `A detailed content flow design including:
  - User Journey Flow Diagram
  - Content Progression Strategy
  - Readability Enhancement Plan
  - Engagement Touchpoint Map
  - Conversion Pathway Design
  - Content Consumption Aids List
  - Interaction Element Specifications
  - Flow Validation Checklist
  - User Experience Optimization Notes`,
  
  agent: contentArchitectAgent
});

export const createContentTemplates = new Task({
  description: `Create reusable content templates for: {topic} and similar content types

  Develop standardized templates that include:

  1. **Article Templates**:
     - How-to guide template
     - Listicle template
     - Comparison/review template
     - Case study template
     - News/update template

  2. **Section Templates**:
     - Introduction template with hooks
     - Problem/solution section template
     - Benefit/feature explanation template
     - FAQ section template
     - Conclusion with CTA template

  3. **Content Elements**:
     - Headline formulas
     - Subheading templates
     - Bullet point structures
     - Quote integration formats
     - Call-out box templates

  4. **SEO Templates**:
     - Title tag templates
     - Meta description templates
     - Header hierarchy templates
     - Internal linking templates

  5. **Quality Assurance**:
     - Content review checklists
     - SEO optimization checklists
     - Readability assessment templates
     - Performance tracking templates

  Create templates that ensure consistency and quality while allowing for customization.`,
  
  expectedOutput: `A comprehensive template library containing:
  - Article Type Templates (5+ formats)
  - Section and Subsection Templates
  - Content Element Templates
  - SEO Optimization Templates
  - Quality Assurance Checklists
  - Customization Guidelines
  - Template Usage Instructions
  - Brand Voice Integration Notes
  - Template Performance Metrics`,
  
  agent: contentArchitectAgent
});

export const optimizeContentStructure = new Task({
  description: `Optimize content structure based on performance data and best practices for: {topic}

  Analyze and enhance the content structure considering:

  1. **Performance Analytics**:
     - User engagement metrics review
     - Time on page and scroll depth analysis
     - Bounce rate and exit point identification
     - Conversion rate optimization opportunities

  2. **Accessibility Improvements**:
     - Screen reader compatibility
     - Keyboard navigation optimization
     - Color contrast and visual hierarchy
     - Alternative text and descriptions

  3. **Mobile Optimization**:
     - Mobile-first content design
     - Touch-friendly interaction elements
     - Loading speed optimization
     - Progressive disclosure techniques

  4. **Content Freshness**:
     - Evergreen content identification
     - Update and refresh strategies
     - Seasonal content adjustments
     - Trending topic integration

  5. **Multi-Channel Adaptation**:
     - Social media snippet optimization
     - Email newsletter formatting
     - PDF/print-friendly versions
     - Audio/video content integration

  Provide specific recommendations for structural improvements.`,
  
  expectedOutput: `A content structure optimization report with:
  - Performance Analysis Summary
  - Structural Improvement Recommendations
  - Accessibility Enhancement Plan
  - Mobile Optimization Checklist
  - Content Freshness Strategy
  - Multi-Channel Adaptation Guide
  - Implementation Priority Matrix
  - Success Metrics Definition
  - Testing and Validation Plan`,
  
  agent: contentArchitectAgent
});

export const validateContentArchitecture = new Task({
  description: `Validate and quality-check the content architecture for: {topic}

  Perform comprehensive validation including:

  1. **Structural Integrity**:
     - Logical flow verification
     - Information hierarchy consistency
     - Content completeness assessment
     - Gap identification and resolution

  2. **User Experience Validation**:
     - Readability and comprehension testing
     - Navigation and usability review
     - Engagement element effectiveness
     - Accessibility compliance check

  3. **SEO Architecture Review**:
     - Header structure optimization
     - Keyword placement validation
     - Internal linking architecture
     - Technical SEO compliance

  4. **Brand Alignment**:
     - Voice and tone consistency
     - Messaging alignment with brand values
     - Visual identity integration
     - Content policy compliance

  5. **Performance Prediction**:
     - User engagement forecasting
     - SEO performance estimation
     - Conversion potential assessment
     - Content lifecycle planning

  Ensure the content architecture meets all quality standards and business objectives.`,
  
  expectedOutput: `A comprehensive validation report containing:
  - Structural Integrity Assessment
  - User Experience Review Summary
  - SEO Architecture Validation
  - Brand Alignment Verification
  - Performance Prediction Analysis
  - Quality Assurance Checklist
  - Issue Resolution Recommendations
  - Final Architecture Approval
  - Implementation Readiness Confirmation`,
  
  agent: contentArchitectAgent
});