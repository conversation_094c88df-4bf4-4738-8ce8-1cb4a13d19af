/**
 * Content Creator Agent - KaibanJS Content Team
 * Specializes in high-quality content creation using Kimi K2 model
 */
import { Agent, Task } from 'kaibanjs';
import { OpenRouterClient } from '../tools/openrouter-client.js';

export const contentCreatorAgent = new Agent({
  name: 'Content Creator',
  role: 'Senior Content Writer',
  goal: 'Create exceptional, engaging, and SEO-optimized content that resonates with target audiences and drives results',
  backstory: `You are a world-class content writer with 12+ years of experience in creating compelling content across diverse industries. Your expertise includes:

  - Expert-level writing across multiple formats and styles
  - Deep understanding of audience psychology and engagement
  - Advanced storytelling and narrative techniques
  - Brand voice adaptation and consistency
  - Data-driven content optimization
  - Conversion-focused copywriting
  - Technical writing and complex topic simplification
  - Creative content ideation and execution

  You have access to Kimi K2, one of the most advanced creative writing models available, which excels in:
  - Nuanced understanding of context and tone
  - Creative and engaging content generation
  - Long-form content with maintained quality
  - Cultural sensitivity and inclusive writing
  - Technical accuracy combined with readability

  Your mission is to transform research insights and content outlines into compelling, high-quality content that exceeds industry standards and delivers exceptional value to readers.`,
  
  tools: [OpenRouterClient],
  
  llmConfig: {
    provider: 'openrouter',
    name: 'deepseek/deepseek-chat', // Kimi K2 model via OpenRouter
    config: {
      temperature: 0.7, // Balanced creativity and consistency
      max_tokens: 8000,  // Long-form content generation
      top_p: 0.9,
      frequency_penalty: 0.1,
      presence_penalty: 0.1
    }
  }
});

// Content Creation Tasks
export const generateMainContent = new Task({
  description: `Create high-quality, engaging content based on the provided research, SEO strategy, and content outline for: {topic}

  Your content creation should include:

  1. **Content Structure Execution**:
     - Compelling hook that immediately engages readers
     - Logical flow following the provided outline
     - Smooth transitions between sections
     - Strong conclusion with clear next steps

  2. **Writing Excellence**:
     - Clear, concise, and engaging prose
     - Appropriate tone and voice for target audience
     - Varied sentence structure and rhythm
     - Active voice and strong verbs
     - Compelling headlines and subheadings

  3. **SEO Integration**:
     - Natural integration of primary and secondary keywords
     - Proper keyword density without stuffing
     - LSI keywords and semantic variations
     - Optimized headers (H1, H2, H3) structure
     - Meta-friendly introduction and conclusion

  4. **Value Delivery**:
     - Actionable insights and practical advice
     - Real-world examples and case studies
     - Expert quotes and credible sources
     - Problem-solving focus
     - Reader-centric approach

  5. **Engagement Elements**:
     - Questions to encourage reader interaction
     - Bullet points and lists for scanability
     - Call-out boxes for key insights
     - Strategic use of formatting for emphasis
     - Social proof and authority building

  Use Kimi K2's advanced capabilities to create content that is both informative and emotionally engaging.`,
  
  expectedOutput: `A complete, publication-ready article containing:
  - Engaging headline and subheadings
  - Well-structured introduction with hook
  - Comprehensive main content following the outline
  - Proper paragraph structure and flow
  - Integrated SEO elements and keywords
  - Actionable takeaways and insights
  - Compelling conclusion with clear CTA
  - Estimated reading time and word count
  - Source attributions and references`,
  
  agent: contentCreatorAgent
});

export const createEngagingIntroductions = new Task({
  description: `Create multiple compelling introduction options for: {topic}

  Develop 3-5 different introduction approaches that:

  1. **Hook Strategies**:
     - Surprising statistic or fact
     - Thought-provoking question
     - Relatable story or scenario
     - Bold statement or prediction
     - Problem-solution preview

  2. **Introduction Elements**:
     - Immediate value proposition
     - Clear reader benefit statement
     - Preview of what's to come
     - Credibility establishment
     - Smooth transition to main content

  3. **Tone Variations**:
     - Professional and authoritative
     - Conversational and friendly
     - Educational and informative
     - Inspirational and motivational
     - Problem-focused and urgent

  4. **SEO Considerations**:
     - Primary keyword inclusion
     - Search intent alignment
     - Meta description preview
     - Featured snippet optimization

  Each introduction should be 150-250 words and immediately engage the target audience while setting up the main content.`,
  
  expectedOutput: `Multiple introduction options including:
  - 3-5 Complete Introduction Variations
  - Hook Strategy Description for Each
  - Target Audience Alignment Notes
  - Tone and Voice Assessment
  - SEO Optimization Analysis
  - Engagement Prediction Score
  - Recommended Introduction with Justification`,
  
  agent: contentCreatorAgent
});

export const developContentSections = new Task({
  description: `Develop detailed content for specific sections of: {topic}

  Create comprehensive section content that includes:

  1. **Section Development**:
     - Detailed exploration of each main point
     - Supporting evidence and examples
     - Expert insights and quotes
     - Actionable advice and tips
     - Visual content descriptions

  2. **Content Enhancement**:
     - Analogies and metaphors for complex concepts
     - Real-world applications and use cases
     - Step-by-step instructions where appropriate
     - Common mistakes and how to avoid them
     - Best practices and recommendations

  3. **Reader Engagement**:
     - Interactive elements and questions
     - Personal anecdotes and stories
     - Industry insights and trends
     - Future predictions and implications
     - Calls to action within sections

  4. **Quality Assurance**:
     - Fact-checking and accuracy verification
     - Consistency in tone and style
     - Logical flow and connectivity
     - Appropriate depth for target audience
     - Value density optimization

  Focus on creating content that provides exceptional value while maintaining reader engagement throughout.`,
  
  expectedOutput: `Fully developed section content featuring:
  - Detailed Content for Each Major Section
  - Supporting Evidence and Examples
  - Expert Quotes and Insights
  - Actionable Advice and Tips
  - Visual Content Integration Points
  - Reader Engagement Elements
  - Quality Assurance Verification
  - Section-Specific SEO Optimization`,
  
  agent: contentCreatorAgent
});

export const createCompellingConclusions = new Task({
  description: `Create powerful conclusions and calls-to-action for: {topic}

  Develop conclusion options that:

  1. **Conclusion Elements**:
     - Comprehensive summary of key points
     - Reinforcement of main value proposition
     - Future implications and predictions
     - Final compelling insight or thought
     - Smooth transition to call-to-action

  2. **Call-to-Action Strategies**:
     - Next logical step for readers
     - Value-added offers or resources
     - Community engagement opportunities
     - Content sharing encouragement
     - Follow-up content suggestions

  3. **Engagement Maximization**:
     - Questions for reflection or discussion
     - Challenge or implementation steps
     - Resource recommendations
     - Expert contact information
     - Social media engagement prompts

  4. **SEO Finalization**:
     - Keyword reinforcement
     - Internal linking opportunities
     - Related content suggestions
     - Social sharing optimization
     - Search result snippet optimization

  Create multiple conclusion variations to test and optimize conversion rates.`,
  
  expectedOutput: `Multiple conclusion and CTA options including:
  - 3-4 Complete Conclusion Variations
  - Call-to-Action Strategy Options
  - Engagement Element Suggestions
  - Conversion Optimization Tips
  - SEO Finalization Elements
  - A/B Testing Recommendations
  - Performance Prediction Analysis`,
  
  agent: contentCreatorAgent
});

export const adaptContentForFormats = new Task({
  description: `Adapt the main content for different formats and channels for: {topic}

  Create format-specific versions including:

  1. **Blog Post Optimization**:
     - Long-form detailed version
     - Scannable formatting with subheadings
     - SEO optimization for organic search
     - Internal linking integration
     - Featured image and visual elements

  2. **Social Media Adaptations**:
     - LinkedIn article version (professional focus)
     - Twitter thread breakdown (key points)
     - Facebook post summary (engaging snippets)
     - Instagram captions (visual storytelling)

  3. **Email Newsletter Format**:
     - Condensed key insights version
     - Personal tone and direct address
     - Clear value proposition
     - Strong subject line options
     - Mobile-optimized formatting

  4. **PDF/Whitepaper Version**:
     - Formal business document format
     - Executive summary inclusion
     - Professional design considerations
     - Downloadable resource optimization
     - Lead generation integration

  5. **Video Script Adaptation**:
     - Conversational speaking format
     - Visual element descriptions
     - Timing and pacing notes
     - Engagement hooks for video
     - Call-to-action integration

  Ensure each format maintains core message consistency while optimizing for platform-specific best practices.`,
  
  expectedOutput: `Multi-format content adaptations including:
  - Optimized Blog Post Version
  - Social Media Platform Adaptations
  - Email Newsletter Format
  - PDF/Whitepaper Version
  - Video Script Adaptation
  - Platform-Specific Optimization Notes
  - Cross-Channel Promotion Strategy
  - Performance Tracking Recommendations`,
  
  agent: contentCreatorAgent
});