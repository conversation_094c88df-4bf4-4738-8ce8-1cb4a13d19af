/**
 * Research Agent - KaibanJS Content Team
 * Specializes in comprehensive topic research and data collection
 */
import { Agent, Task } from 'kaibanjs';
import { EnhancedTavilySearch } from '../tools/tavily-search.js';

export const researchAgent = new Agent({
  name: 'Research Agent',
  role: 'Lead Researcher',
  goal: 'Conduct comprehensive research on topics to gather accurate, up-to-date, and relevant information',
  backstory: `You are an expert research analyst with years of experience in gathering and analyzing information from diverse sources. Your expertise lies in:

  - Conducting thorough web research using advanced search techniques
  - Identifying credible and authoritative sources
  - Extracting key insights and data points from complex information
  - Organizing research findings into actionable intelligence
  - Fact-checking and verifying information accuracy
  - Understanding context and implications of research findings

  You have access to enhanced search capabilities through Tavily API and can perform deep research across multiple domains including technology, business, science, healthcare, and more.`,
  
  tools: [EnhancedTavilySearch],
  
  llmConfig: {
    provider: 'openrouter',
    name: 'anthropic/claude-3-haiku',
    config: {
      temperature: 0.1, // Low temperature for factual accuracy
      max_tokens: 2000
    }
  }
});

// Research Tasks
export const conductTopicResearch = new Task({
  description: `Conduct comprehensive research on the given topic: {topic}

  Your research should include:
  1. **Background Information**: Historical context, definitions, and fundamental concepts
  2. **Current State**: Latest developments, trends, and current market conditions
  3. **Key Statistics**: Important numbers, data points, and metrics
  4. **Expert Opinions**: Quotes and insights from industry leaders and experts
  5. **Case Studies**: Real-world examples and success stories
  6. **Challenges and Opportunities**: Current problems and future possibilities
  7. **Competitive Landscape**: Key players and market dynamics
  8. **Future Trends**: Predictions and emerging developments

  Use multiple search queries to gather comprehensive information from:
  - Authoritative websites and publications
  - Industry reports and whitepapers
  - News articles and press releases
  - Academic papers and research studies
  - Expert interviews and opinion pieces

  Organize your findings into a structured research report that will serve as the foundation for content creation.`,
  
  expectedOutput: `A comprehensive research report containing:
  - Executive Summary (2-3 sentences)
  - Key Findings (5-7 bullet points)
  - Detailed Research Sections organized by subtopics
  - Source Citations with URLs
  - Data Points and Statistics
  - Expert Quotes and Insights
  - Suggested Content Angles
  - Related Topics for Further Exploration`,
  
  agent: researchAgent
});

export const gatherSupportingData = new Task({
  description: `Gather supporting data and evidence for content on: {topic}

  Focus on collecting:
  1. **Quantitative Data**: Statistics, survey results, market data, performance metrics
  2. **Qualitative Insights**: Expert opinions, user testimonials, case study details
  3. **Visual Content Sources**: Charts, graphs, infographics, images that could enhance the content
  4. **Comparative Analysis**: How this topic compares to alternatives or competitors
  5. **Regulatory Information**: Relevant laws, compliance requirements, industry standards
  6. **Best Practices**: Proven methodologies and successful implementation strategies

  Verify the credibility of all sources and prioritize recent, authoritative information.`,
  
  expectedOutput: `A structured data collection report with:
  - Statistical Data Points with sources
  - Expert Quotes and Attribution
  - Case Study Summaries
  - Comparative Analysis Tables
  - Best Practices List
  - Regulatory/Compliance Notes
  - Visual Content Recommendations
  - Source Credibility Assessment`,
  
  agent: researchAgent
});

export const factCheck = new Task({
  description: `Perform fact-checking and verification on the research findings for: {topic}

  Your fact-checking should include:
  1. **Source Verification**: Confirm the credibility and authority of information sources
  2. **Data Accuracy**: Cross-reference statistics and data points with multiple sources
  3. **Date Relevance**: Ensure information is current and not outdated
  4. **Expert Validation**: Verify that quoted experts are legitimate and accurately represented
  5. **Claim Substantiation**: Confirm that all major claims are backed by evidence
  6. **Bias Detection**: Identify potential bias in sources and account for it
  7. **Contradiction Resolution**: Address any conflicting information found

  Flag any questionable information and provide alternative sources where needed.`,
  
  expectedOutput: `A fact-checking report containing:
  - Verified Facts List
  - Source Credibility Ratings
  - Flagged Questionable Claims
  - Alternative Source Recommendations
  - Bias Assessment Notes
  - Confidence Level for Each Major Point
  - Recommendations for Additional Verification`,
  
  agent: researchAgent
});