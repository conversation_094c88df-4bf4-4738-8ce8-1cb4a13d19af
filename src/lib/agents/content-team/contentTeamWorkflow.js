/**
 * Content Team Workflow - KaibanJS + OpenRouter <PERSON><PERSON>
 * Orchestrates the 5-agent content creation system
 */
import { Team } from 'kaibanjs';
import { 
  researchAgent, 
  conductTopicResearch, 
  gatherSupportingData, 
  factCheck 
} from './agents/research-agent.js';

import { 
  contentArchitectAgent, 
  createContentOutline, 
  designContentFlow, 
  validateContentArchitecture 
} from './agents/content-architect.js';
import { 
  contentCreatorAgent, 
  generateMainContent, 
  createEngagingIntroductions, 
  adaptContentForFormats 
} from './agents/content-creator.js';
import {
  qualityEditorAgent,
  comprehensiveContentReview,
  finalQualityAssurance
} from './agents/quality-editor.js';

export class ContentTeamWorkflow {
  constructor(config = {}) {
    this.config = {
      maxTokens: 8000,
      temperature: 0.7,
      enableCostTracking: true,
      enableRealTimeUpdates: true,
      ...config
    };
    
    this.initializeTeam();
    this.setupEventHandlers();
  }

  initializeTeam() {
    // Create the content writing team
    this.contentTeam = new Team({
      name: 'Professional Content Writing Team',
      agents: [
        researchAgent,
        contentArchitectAgent,
        contentCreatorAgent,
        qualityEditorAgent
      ],
      tasks: [
        // Phase 1: Research & Analysis
        conductTopicResearch,
        gatherSupportingData,

        // Phase 2: Content Architecture
        createContentOutline,
        designContentFlow,
        
        // Phase 3: Content Creation
        generateMainContent,
        createEngagingIntroductions,
        
        // Phase 4: Quality Assurance
        comprehensiveContentReview,
        factCheck,
        
        // Phase 5: Final Review & Optimization
        validateContentArchitecture,
        finalQualityAssurance,
        adaptContentForFormats
      ],
      env: {
        OPENROUTER_API_KEY: process.env.NEXT_PUBLIC_OPENROUTER_API_KEY,
        TAVILY_API_KEY: process.env.NEXT_PUBLIC_TAVILY_API_KEY
      }
    });
  }

  setupEventHandlers() {
    if (!this.config.enableRealTimeUpdates) return;

    // Set up real-time progress tracking
    this.contentTeam.on('task:start', (data) => {
      this.onTaskStart(data);
    });

    this.contentTeam.on('task:progress', (data) => {
      this.onTaskProgress(data);
    });

    this.contentTeam.on('task:complete', (data) => {
      this.onTaskComplete(data);
    });

    this.contentTeam.on('task:error', (data) => {
      this.onTaskError(data);
    });

    this.contentTeam.on('team:complete', (data) => {
      this.onTeamComplete(data);
    });
  }

  // Event handlers for real-time updates
  onTaskStart(data) {
    console.log(`🚀 Task started: ${data.taskName} by ${data.agentName}`);
    this.broadcastUpdate({
      type: 'task_start',
      task: data.taskName,
      agent: data.agentName,
      timestamp: new Date().toISOString()
    });
  }

  onTaskProgress(data) {
    console.log(`⏳ Task progress: ${data.taskName} - ${data.progress}%`);
    this.broadcastUpdate({
      type: 'task_progress',
      task: data.taskName,
      agent: data.agentName,
      progress: data.progress,
      timestamp: new Date().toISOString()
    });
  }

  onTaskComplete(data) {
    console.log(`✅ Task completed: ${data.taskName} by ${data.agentName}`);
    if (this.config.enableCostTracking) {
      this.trackCost(data);
    }
    this.broadcastUpdate({
      type: 'task_complete',
      task: data.taskName,
      agent: data.agentName,
      output: data.output,
      cost: data.cost,
      duration: data.duration,
      timestamp: new Date().toISOString()
    });
  }

  onTaskError(data) {
    console.error(`❌ Task error: ${data.taskName} - ${data.error}`);
    this.broadcastUpdate({
      type: 'task_error',
      task: data.taskName,
      agent: data.agentName,
      error: data.error,
      timestamp: new Date().toISOString()
    });
  }

  onTeamComplete(data) {
    console.log('🎉 Content team workflow completed!');
    const summary = this.generateWorkflowSummary(data);
    this.broadcastUpdate({
      type: 'workflow_complete',
      summary,
      timestamp: new Date().toISOString()
    });
  }

  // Main workflow execution method
  async executeWorkflow(contentRequest) {
    try {
      console.log('🎬 Starting content creation workflow...');
      
      const startTime = Date.now();
      
      // Prepare the input context
      const context = {
        topic: contentRequest.topic,
        targetAudience: contentRequest.targetAudience,
        contentType: contentRequest.contentType,
        wordCount: contentRequest.wordCount,
        tone: contentRequest.tone,
        additionalRequirements: contentRequest.additionalRequirements
      };

      // Execute the team workflow
      const result = await this.contentTeam.start(context);
      
      const endTime = Date.now();
      const totalDuration = (endTime - startTime) / 1000; // in seconds

      // Process and structure the results
      const processedResult = this.processWorkflowResult(result, totalDuration);
      
      return {
        success: true,
        data: processedResult,
        executionTime: totalDuration
      };

    } catch (error) {
      console.error('Workflow execution error:', error);
      return {
        success: false,
        error: error.message,
        stack: error.stack
      };
    }
  }

  // Process and structure the workflow results
  processWorkflowResult(rawResult, duration) {
    const agents = rawResult.agents || [];
    const tasks = rawResult.tasks || [];
    
    // Extract the final content from the content creator
    const finalContent = this.extractFinalContent(tasks);
    
    // Calculate total cost
    const totalCost = this.calculateTotalCost(tasks);
    
    // Generate SEO and readability scores
    const seoScore = this.calculateSeoScore(tasks);
    const readabilityScore = this.calculateReadabilityScore(tasks);
    
    // Create agent status summary
    const agentSummary = agents.map(agent => ({
      id: agent.id,
      name: agent.name,
      status: agent.status,
      tasksCompleted: agent.completedTasks?.length || 0,
      totalCost: agent.totalCost || 0,
      avgDuration: agent.avgDuration || 0
    }));

    return {
      finalContent,
      seoScore,
      readabilityScore,
      totalCost,
      totalDuration: duration,
      agents: agentSummary,
      taskResults: tasks.map(task => ({
        name: task.name,
        status: task.status,
        output: task.output,
        cost: task.cost,
        duration: task.duration
      })),
      metadata: {
        wordCount: this.countWords(finalContent),
        estimatedReadingTime: this.calculateReadingTime(finalContent),
        keywordsUsed: this.extractUsedKeywords(finalContent),
        generatedAt: new Date().toISOString()
      }
    };
  }

  // Utility methods
  extractFinalContent(tasks) {
    const contentTask = tasks.find(task => 
      task.name === 'generateMainContent' || 
      task.agentName === 'Content Creator'
    );
    return contentTask?.output || '';
  }

  calculateTotalCost(tasks) {
    return tasks.reduce((total, task) => total + (task.cost || 0), 0);
  }

  calculateSeoScore(tasks) {
    // Basic SEO score based on content structure and quality
    const contentTask = tasks.find(task =>
      task.name === 'comprehensiveContentReview' ||
      task.agentName === 'Quality Editor'
    );
    return contentTask?.seoScore || 80; // Default basic SEO score
  }

  calculateReadabilityScore(tasks) {
    const readabilityTask = tasks.find(task => 
      task.name === 'readabilityAndEngagementOptimization'
    );
    return readabilityTask?.readabilityScore || 78; // Default score
  }

  countWords(content) {
    if (!content) return 0;
    return content.trim().split(/\s+/).length;
  }

  calculateReadingTime(content) {
    const wordsPerMinute = 200;
    const wordCount = this.countWords(content);
    return Math.ceil(wordCount / wordsPerMinute);
  }

  extractUsedKeywords(content) {
    // Simple keyword extraction (in real implementation, use NLP)
    if (!content) return [];
    const words = content.toLowerCase().match(/\b\w{3,}\b/g) || [];
    const frequency = {};
    words.forEach(word => {
      frequency[word] = (frequency[word] || 0) + 1;
    });
    return Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);
  }

  trackCost(taskData) {
    // Implement cost tracking logic
    const cost = this.estimateTaskCost(taskData);
    taskData.cost = cost;
    
    // Store cost data for analytics
    this.storeCostData(taskData);
  }

  estimateTaskCost(taskData) {
    // Estimate cost based on model usage
    // Kimi K2 pricing: ~$0.60/1M input tokens, ~$2.50/1M output tokens
    const inputTokens = taskData.inputTokens || 1000;
    const outputTokens = taskData.outputTokens || 2000;
    
    const inputCost = (inputTokens / 1000000) * 0.60;
    const outputCost = (outputTokens / 1000000) * 2.50;
    
    return inputCost + outputCost;
  }

  storeCostData(taskData) {
    // Store cost data for analytics (implement with your database)
    console.log(`💰 Cost tracked for ${taskData.taskName}: $${taskData.cost.toFixed(4)}`);
  }

  broadcastUpdate(update) {
    // Broadcast real-time updates to connected clients
    if (this.updateCallback) {
      this.updateCallback(update);
    }
  }

  setUpdateCallback(callback) {
    this.updateCallback = callback;
  }

  generateWorkflowSummary(data) {
    return {
      totalTasks: data.tasks?.length || 0,
      completedTasks: data.tasks?.filter(t => t.status === 'completed').length || 0,
      totalCost: this.calculateTotalCost(data.tasks || []),
      totalDuration: data.duration || 0,
      successRate: data.successRate || 100
    };
  }

  // Quick test method for development
  async testWorkflow() {
    const testRequest = {
      topic: 'Artificial Intelligence in Healthcare',
      targetAudience: 'Healthcare professionals and tech enthusiasts',
      contentType: 'blog-post',
      wordCount: 2000,
      tone: 'professional',
      additionalRequirements: 'Include recent case studies and expert opinions'
    };

    console.log('🧪 Running workflow test...');
    const result = await this.executeWorkflow(testRequest);
    console.log('Test result:', result);
    return result;
  }
}

// Export a default instance
export const contentTeamWorkflow = new ContentTeamWorkflow({
  enableCostTracking: true,
  enableRealTimeUpdates: true
});

// Export the class for custom instances
export default ContentTeamWorkflow;