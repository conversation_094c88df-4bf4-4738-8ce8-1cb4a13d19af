/**
 * Enhanced YouTube Service for Video Data and Caption Extraction
 * Uses youtubei.js for accessing YouTube's private InnerTube API
 */

import axios from 'axios';
import { Innertube } from 'youtubei.js';
import { SupadataApiKeyRotator } from './supadata-rotator';

interface YouTubeVideo {
  id: string;
  title: string;
  description: string;
  channelTitle: string;
  viewCount: string;
  likeCount?: string;
  publishedAt: string;
  duration: string;
  thumbnailUrl: string;
}

interface YouTubeCaption {
  text: string;
  start: number;
  duration: number;
}

interface YouTubeSearchResult {
  videos: YouTubeVideo[];
  totalResults: number;
}

interface CaptionTrack {
  id: string;
  name: string;
  languageCode: string;
  kind: string;
}

export class YouTubeService {
  private apiKey: string;
  private supadataRotator: SupadataApiKeyRotator;
  private innertube: any = null;

  constructor() {
    // Use the provided API key
    this.apiKey = process.env.YOUTUBE_API_KEY || '';
    
    if (!this.apiKey) {
      console.warn('⚠️ YouTube API key not found in environment variables');
    }
    this.supadataRotator = new SupadataApiKeyRotator();
    
    if (!this.apiKey) {
      console.warn('YouTube API key not configured');
    }
    
    console.log('✅ YouTube service initialized with Supadata API rotation');
  }

  /**
   * Initialize the Innertube client
   */
  private async initInnerTube() {
    if (!this.innertube) {
      try {
        console.log('🔧 Initializing YouTube InnerTube client...');
        this.innertube = await Innertube.create();
        console.log('✅ InnerTube client initialized successfully');
      } catch (error) {
        console.error('❌ Failed to initialize InnerTube client:', error);
        this.innertube = null;
      }
    }
    return this.innertube;
  }

  /**
   * Search for YouTube videos by topic
   */
  async searchVideos(
    query: string, 
    maxResults: number = 10
  ): Promise<YouTubeSearchResult> {
    try {
      // Try InnerTube search first
      const innertube = await this.initInnerTube();
      if (innertube) {
        // Store original console functions
        const originalConsole = console.warn;
        const originalError = console.error;
        
        try {
          console.log(`🔍 Searching via InnerTube: "${query}"`);
          
          // Suppress YouTube.js parser warnings temporarily
          console.warn = (...args) => {
            const message = args.join(' ');
            if (!message.includes('[YOUTUBEJS][Parser]') && !message.includes('VideoSummaryContentView') && !message.includes('VideoSummaryParagraphView')) {
              originalConsole(...args);
            }
          };
          console.error = (...args) => {
            const message = args.join(' ');
            if (!message.includes('[YOUTUBEJS][Parser]') && !message.includes('VideoSummaryContentView') && !message.includes('VideoSummaryParagraphView')) {
              originalError(...args);
            }
          };
          
          const searchResults = await innertube.search(query, { type: 'video' });
          
          if (searchResults && searchResults.videos) {
            const videos: YouTubeVideo[] = searchResults.videos.slice(0, maxResults).map((video: any) => ({
              id: video.id,
              title: video.title?.text || video.title || 'Unknown Title',
              description: video.description?.text || video.description || '',
              channelTitle: video.author?.name || video.channel?.name || 'Unknown Channel',
              viewCount: video.view_count?.text || video.views?.text || '0',
              likeCount: video.like_count?.text || undefined,
              publishedAt: video.published?.text || new Date().toISOString(),
              duration: video.duration?.text || '0:00',
              thumbnailUrl: video.thumbnails?.[0]?.url || video.thumbnail?.url || ''
            }));

            console.log(`✅ InnerTube search found ${videos.length} videos`);
            return {
              videos,
              totalResults: videos.length
            };
          }
        } catch (innertubeError) {
          // Only log if it's not a known parser issue
          const errorMessage = innertubeError instanceof Error ? innertubeError.message : String(innertubeError);
          if (!errorMessage.includes('VideoSummaryContentView') && !errorMessage.includes('Parser')) {
            console.warn('⚠️ InnerTube search failed, falling back to official API:', innertubeError);
          } else {
            console.log('🔄 YouTube parser issue detected, falling back to official API...');
          }
        } finally {
          // Always restore console functions
          console.warn = originalConsole;
          console.error = originalError;
        }
      }

      // Fallback to official YouTube Data API
      if (!this.apiKey) {
        return this.getMockSearchResults(query);
      }

      console.log(`🔍 Searching via YouTube Data API: "${query}"`);
      const searchUrl = 'https://www.googleapis.com/youtube/v3/search';
      const searchResponse = await axios.get(searchUrl, {
        params: {
          key: this.apiKey,
          q: query,
          part: 'snippet',
          type: 'video',
          maxResults,
          order: 'relevance',
          videoDuration: 'medium'
        }
      });

      const videoIds = searchResponse.data.items.map((item: any) => item.id.videoId).join(',');
      
      const detailsUrl = 'https://www.googleapis.com/youtube/v3/videos';
      const detailsResponse = await axios.get(detailsUrl, {
        params: {
          key: this.apiKey,
          id: videoIds,
          part: 'snippet,statistics,contentDetails'
        }
      });

      const videos: YouTubeVideo[] = detailsResponse.data.items.map((item: any) => ({
        id: item.id,
        title: item.snippet.title,
        description: item.snippet.description,
        channelTitle: item.snippet.channelTitle,
        viewCount: item.statistics.viewCount,
        likeCount: item.statistics.likeCount,
        publishedAt: item.snippet.publishedAt,
        duration: this.parseDuration(item.contentDetails.duration),
        thumbnailUrl: item.snippet.thumbnails.high.url
      }));

      console.log(`✅ YouTube Data API search found ${videos.length} videos`);
      return {
        videos,
        totalResults: searchResponse.data.pageInfo.totalResults
      };
    } catch (error) {
      console.error('YouTube search error:', error);
      throw new Error('Failed to search YouTube videos');
    }
  }

  /**
   * Extract captions from a YouTube video using Supadata.ai API
   */
  async extractCaptions(videoId: string, language: string = 'en'): Promise<YouTubeCaption[]> {
    try {
      console.log(`🎬 Extracting captions for video: ${videoId} using Supadata.ai API`);
      
      // Try Supadata API first (most reliable)
      const supadataCaptions = await this.extractCaptionsSupadata(videoId, language);
      if (supadataCaptions.length > 0) {
        return supadataCaptions;
      }

      // Fallback to InnerTube API
      console.log('🔄 Supadata failed, trying InnerTube API...');
      const innertubeCaptions = await this.extractCaptionsInnerTube(videoId, language);
      if (innertubeCaptions.length > 0) {
        return innertubeCaptions;
      }

      // Final fallback to YouTube Data API
      console.log('🔄 InnerTube failed, trying YouTube Data API...');
      return this.extractCaptionsDataAPI(videoId, language);

    } catch (error) {
      console.error('💥 Caption extraction error:', error);
      
      return [{
        text: `[Caption extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}]`,
        start: 0,
        duration: 5
      }];
    }
  }

  /**
   * Extract captions using Supadata.ai API with rotation (primary method)
   */
  private async extractCaptionsSupadata(videoId: string, language: string = 'en'): Promise<YouTubeCaption[]> {
    const maxRetries = 3;
    let lastError: any = null;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        const currentApiKey = this.supadataRotator.getCurrentApiKey();
        console.log(`🌟 Using Supadata.ai API for video: ${videoId} (attempt ${attempt + 1}/${maxRetries}, key: ...${currentApiKey.slice(-4)})`);
        
        const response = await axios.get(`https://api.supadata.ai/v1/youtube/transcript`, {
          params: {
            videoId: videoId,
            lang: language
          },
          headers: {
            'x-api-key': currentApiKey,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        });

        if (!response.data || !response.data.content) {
          console.warn(`⚠️ No transcript content received from Supadata for ${videoId}`);
          return [];
        }

        // Convert Supadata format to our caption format
        const captions: YouTubeCaption[] = response.data.content.map((item: any) => ({
          text: item.text || '',
          start: (item.offset || 0) / 1000, // Convert milliseconds to seconds
          duration: (item.duration || 2000) / 1000 // Convert milliseconds to seconds
        })).filter((caption: YouTubeCaption) => caption.text.trim());

        if (captions.length > 0) {
          console.log(`✅ Successfully extracted ${captions.length} caption segments via Supadata.ai (key: ...${currentApiKey.slice(-4)})`);
          console.log(`📄 Language: ${response.data.lang || 'unknown'}`);
          console.log(`🌍 Available languages: ${response.data.availableLangs?.join(', ') || 'N/A'}`);
          console.log(`📋 Sample text: "${captions[0].text.substring(0, 100)}..."`);
          return captions;
        } else {
          console.warn(`⚠️ No valid caption segments found in Supadata response`);
          return [];
        }

      } catch (error) {
        lastError = error;
        const currentApiKey = this.supadataRotator.getCurrentApiKey();
        
        if (axios.isAxiosError(error)) {
          // Use the rotator's error handling
          const shouldRetry = this.supadataRotator.handleSupadataError(currentApiKey, error);
          
          if (!shouldRetry) {
            console.log(`🛑 Supadata error indicates no retry needed: ${error.response?.status}`);
            break; // Don't retry for certain errors (like 404)
          }
          
          if (attempt < maxRetries - 1) {
            console.log(`🔄 Retrying with rotated Supadata API key...`);
            continue; // Try again with rotated key
          }
        } else {
          this.supadataRotator.markKeyError(currentApiKey, 'network_error');
          console.error(`❌ Supadata request failed:`, error instanceof Error ? error.message : String(error));
          
          if (attempt < maxRetries - 1) {
            console.log(`🔄 Retrying with different Supadata API key...`);
            continue;
          }
        }
      }
    }

    // All retries failed
    console.error(`❌ All Supadata API attempts failed for video ${videoId}`);
    if (lastError && axios.isAxiosError(lastError)) {
      console.error(`📄 Final error: ${lastError.response?.status} - ${lastError.response?.statusText}`);
    }
    
    return [];
  }

  /**
   * Extract captions using youtubei.js InnerTube API (fallback method)
   */
  private async extractCaptionsInnerTube(videoId: string, language: string = 'en'): Promise<YouTubeCaption[]> {
    try {
      console.log(`🔧 Using InnerTube API fallback for video: ${videoId}`);
      
      const innertube = await this.initInnerTube();
      if (!innertube) {
        console.warn('⚠️ InnerTube client not available');
        return [];
      }

      // Get video info with captions
      const videoInfo = await innertube.getInfo(videoId);
      
      if (!videoInfo) {
        console.warn(`⚠️ Could not get video info for ${videoId}`);
        return [];
      }

      console.log(`📺 Video: "${videoInfo.basic_info?.title}"`);

      // Check for captions
      const captionTracks = videoInfo.captions?.caption_tracks || [];
      
      if (captionTracks.length === 0) {
        console.warn(`⚠️ No caption tracks available for video ${videoId}`);
        return [];
      }

      console.log(`📝 Found ${captionTracks.length} caption track(s)`);

      // Find the best caption track
      let selectedTrack = captionTracks.find((track: any) => 
        track.language_code === language || track.language_code?.startsWith(language)
      );
      
      if (!selectedTrack) {
        selectedTrack = captionTracks.find((track: any) => 
          track.language_code?.startsWith('en')
        );
      }
      
      if (!selectedTrack) {
        selectedTrack = captionTracks[0];
      }

      console.log(`🎯 Selected track: ${selectedTrack.name?.text || selectedTrack.name} (${selectedTrack.language_code})`);

      // Note: getTranscript method doesn't exist in current youtubei.js version
      // This is kept for future compatibility but will likely fail
      try {
        const transcript = await (innertube as any).getTranscript(videoId, selectedTrack.language_code);
        
        if (transcript?.content?.body?.initial_segments) {
          const captions: YouTubeCaption[] = transcript.content.body.initial_segments
            .map((segment: any) => ({
              text: segment.snippet?.text || '',
              start: segment.start_ms ? segment.start_ms / 1000 : 0,
              duration: segment.end_ms && segment.start_ms ? (segment.end_ms - segment.start_ms) / 1000 : 2
            }))
            .filter((caption: YouTubeCaption) => caption.text.trim());
          
          if (captions.length > 0) {
            console.log(`✅ Successfully extracted ${captions.length} caption segments via InnerTube`);
            return captions;
          }
        }
             } catch (transcriptError) {
         console.warn('⚠️ InnerTube getTranscript method not available:', transcriptError instanceof Error ? transcriptError.message : String(transcriptError));
       }

      return [];

    } catch (error) {
      console.error('💥 InnerTube fallback error:', error);
      return [];
    }
  }

  /**
   * Fallback method using YouTube Data API (original implementation)
   */
  private async extractCaptionsDataAPI(videoId: string, language: string = 'en'): Promise<YouTubeCaption[]> {
    try {
      console.log(`🔄 Using YouTube Data API fallback for video: ${videoId}`);

      if (!this.apiKey) {
        console.warn('⚠️ No YouTube API key available');
        return [];
      }

      const captionTracks = await this.getCaptionTracks(videoId);
      
      if (captionTracks.length === 0) {
        console.warn(`⚠️ No caption tracks available for video ${videoId}`);
        return [];
      }

      console.log(`📝 Found ${captionTracks.length} caption track(s) via Data API`);

      let selectedTrack = captionTracks.find(track => track.languageCode === language);
      if (!selectedTrack) {
        selectedTrack = captionTracks.find(track => track.languageCode.startsWith('en'));
      }
      if (!selectedTrack) {
        selectedTrack = captionTracks[0];
      }

      console.log(`🎯 Selected caption track: ${selectedTrack.name} (${selectedTrack.languageCode})`);

      const captions = await this.downloadCaptions(selectedTrack.id);
      
      if (captions.length > 0) {
        console.log(`✅ Successfully extracted ${captions.length} caption segments via Data API`);
      }

      return captions;

    } catch (error) {
      console.error('💥 Data API fallback error:', error);
      return [{
        text: '[Captions require OAuth authentication - only video owners can download caption content via API]',
        start: 0,
        duration: 5
      }];
    }
  }

  /**
   * Get available caption tracks for a video using YouTube Data API
   */
  async getCaptionTracks(videoId: string): Promise<CaptionTrack[]> {
    try {
      const captionsUrl = 'https://www.googleapis.com/youtube/v3/captions';
      const response = await axios.get(captionsUrl, {
        params: {
          key: this.apiKey,
          videoId: videoId,
          part: 'snippet'
        }
      });

      const tracks: CaptionTrack[] = response.data.items.map((item: any) => ({
        id: item.id,
        name: item.snippet.name,
        languageCode: item.snippet.language,
        kind: item.snippet.trackKind
      }));

      return tracks;
    } catch (error) {
      console.error('Failed to get caption tracks:', error);
      
      // If API fails, try to get available languages using the old method as fallback
      const languages = await this.getAvailableLanguagesFallback(videoId);
      return languages.map((lang, index) => ({
        id: `${videoId}_${lang}_${index}`,
        name: `${lang} (auto-generated)`,
        languageCode: lang,
        kind: 'standard'
      }));
    }
  }

  /**
   * Download caption content using YouTube Data API
   */
  async downloadCaptions(captionId: string): Promise<YouTubeCaption[]> {
    try {
      // Note: This endpoint requires OAuth authentication for most videos
      // For public videos, we'll try with API key first
      const downloadUrl = `https://www.googleapis.com/youtube/v3/captions/${captionId}`;
      
      const response = await axios.get(downloadUrl, {
        params: {
          key: this.apiKey,
          tfmt: 'ttml' // Request TTML format for timing information
        }
      });

      // Parse the TTML/SRT response
      return this.parseCaptionContent(response.data);

    } catch (error) {
      console.error('Caption download failed:', error);
      
      // YouTube Data API captions.download requires OAuth for most content
      // This is expected for most videos that aren't owned by the API key owner
      if (axios.isAxiosError(error) && error.response?.status === 403) {
        console.warn('🔒 Caption download requires OAuth authentication (expected for most videos)');
        return [{
          text: '[Captions require OAuth authentication - only video owners can download caption content via API]',
          start: 0,
          duration: 5
        }];
      }
      
      throw error;
    }
  }

  /**
   * Parse caption content from various formats (TTML, SRT, VTT)
   */
  private parseCaptionContent(content: string): YouTubeCaption[] {
    const captions: YouTubeCaption[] = [];
    
    try {
      // Check if it's TTML format
      if (content.includes('<tt') || content.includes('<ttml')) {
        return this.parseTTML(content);
      }
      
      // Check if it's SRT format
      if (content.includes('-->')) {
        return this.parseSRT(content);
      }
      
      // Check if it's VTT format
      if (content.includes('WEBVTT')) {
        return this.parseVTT(content);
      }
      
      // If it's plain text, create a single caption
      if (content.trim()) {
        return [{
          text: content.trim(),
          start: 0,
          duration: 10
        }];
      }
      
    } catch (error) {
      console.error('Caption parsing error:', error);
    }
    
    return captions;
      }

  /**
   * Parse TTML format captions
   */
  private parseTTML(content: string): YouTubeCaption[] {
      const captions: YouTubeCaption[] = [];
      
    try {
      // Extract <p> elements with timing information
      const pElements = content.match(/<p[^>]*begin="([^"]*)"[^>]*dur="([^"]*)"[^>]*>([\s\S]*?)<\/p>/g);
      
      if (pElements) {
        for (const element of pElements) {
          const beginMatch = element.match(/begin="([^"]*)"/);
          const durMatch = element.match(/dur="([^"]*)"/);
          const textMatch = element.match(/<p[^>]*>([\s\S]*?)<\/p>/);
          
          if (beginMatch && durMatch && textMatch) {
            const start = this.parseTimeToSeconds(beginMatch[1]);
            const duration = this.parseTimeToSeconds(durMatch[1]);
            const text = textMatch[1].replace(/<[^>]*>/g, '').trim();
            
            if (text) {
              captions.push({ text, start, duration });
            }
          }
        }
      }
    } catch (error) {
      console.error('TTML parsing error:', error);
    }
    
    return captions;
  }

  /**
   * Parse SRT format captions
   */
  private parseSRT(content: string): YouTubeCaption[] {
    const captions: YouTubeCaption[] = [];
    const blocks = content.split(/\n\s*\n/);
    
    for (const block of blocks) {
      const lines = block.trim().split('\n');
      if (lines.length >= 3) {
        const timeMatch = lines[1].match(/(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})/);
        if (timeMatch) {
          const start = this.srtTimeToSeconds(timeMatch[1]);
          const end = this.srtTimeToSeconds(timeMatch[2]);
          const text = lines.slice(2).join(' ').replace(/<[^>]*>/g, '').trim();

          if (text) {
            captions.push({
              text,
              start,
              duration: end - start
            });
          }
        }
      }
    }
    
    return captions;
  }

  /**
   * Parse VTT format captions
   */
  private parseVTT(content: string): YouTubeCaption[] {
    const captions: YouTubeCaption[] = [];
    const lines = content.split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      const timeMatch = line.match(/(\d{2}:\d{2}:\d{2}\.\d{3}) --> (\d{2}:\d{2}:\d{2}\.\d{3})/);
      
      if (timeMatch && i + 1 < lines.length) {
        const start = this.vttTimeToSeconds(timeMatch[1]);
        const end = this.vttTimeToSeconds(timeMatch[2]);
        const text = lines[i + 1].replace(/<[^>]*>/g, '').trim();
        
        if (text) {
          captions.push({
            text,
            start,
            duration: end - start
            });
          }
        }
      }

      return captions;
  }

  /**
   * Convert time format to seconds
   */
  private parseTimeToSeconds(timeStr: string): number {
    if (timeStr.includes('s')) {
      return parseFloat(timeStr.replace('s', ''));
    }
    
    const parts = timeStr.split(':');
    if (parts.length === 3) {
      return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseFloat(parts[2]);
    }
    
    return 0;
  }

  /**
   * Convert SRT time format to seconds
   */
  private srtTimeToSeconds(timeStr: string): number {
    const [time, ms] = timeStr.split(',');
    const [hours, minutes, seconds] = time.split(':').map(Number);
    return hours * 3600 + minutes * 60 + seconds + Number(ms) / 1000;
  }

  /**
   * Convert VTT time format to seconds
   */
  private vttTimeToSeconds(timeStr: string): number {
    const [hours, minutes, secondsMs] = timeStr.split(':');
    const [seconds, ms] = secondsMs.split('.');
    return Number(hours) * 3600 + Number(minutes) * 60 + Number(seconds) + Number(ms) / 1000;
  }

  /**
   * Fallback method to get available languages (for when API fails)
   */
  async getAvailableLanguagesFallback(videoId: string): Promise<string[]> {
    try {
      const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;
      const response = await axios.get(videoUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      const html = response.data;
      const playerResponseMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?})\s*;\s*(?:var\s+(?:meta|head)|<\/script|\n)/);
      
      if (!playerResponseMatch) {
        return [];
      }

      const playerResponse = JSON.parse(playerResponseMatch[1]);
      const tracks = playerResponse.captions?.playerCaptionsTracklistRenderer?.captionTracks || [];
      
      return tracks.map((track: any) => track.languageCode).filter(Boolean);
    } catch (error) {
      console.error('Failed to get available languages:', error);
      return [];
    }
  }

  /**
   * Get available caption languages for a video (deprecated - use getCaptionTracks instead)
   */
  async getAvailableLanguages(videoId: string): Promise<string[]> {
    const tracks = await this.getCaptionTracks(videoId);
    return tracks.map(track => track.languageCode);
  }

  /**
   * Parse ISO 8601 duration to human-readable format
   */
  private parseDuration(duration: string): string {
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return duration;

    const hours = parseInt(match[1] || '0');
    const minutes = parseInt(match[2] || '0');
    const seconds = parseInt(match[3] || '0');

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  /**
   * Get mock search results for development
   */
  private getMockSearchResults(query: string): YouTubeSearchResult {
    return {
      videos: [
        {
          id: 'dQw4w9WgXcQ',
          title: `How to ${query} - Complete Guide 2024 🚀`,
          description: 'This is a comprehensive guide covering everything you need to know about the topic with real examples and practical tips.',
          channelTitle: 'Tech Masterclass',
          viewCount: '1547823',
          likeCount: '89421',
          publishedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          duration: '12:34',
          thumbnailUrl: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/hqdefault.jpg'
        },
        {
          id: 'jNQXAC9IVRw',
          title: `${query} for Beginners - Everything You Need to Know ✨`,
          description: 'Learn the fundamentals with step-by-step instructions, practical examples, and expert tips from industry professionals.',
          channelTitle: 'Learning Hub Pro',
          viewCount: '892156',
          likeCount: '34521',
          publishedAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
          duration: '15:42',
          thumbnailUrl: 'https://i.ytimg.com/vi/jNQXAC9IVRw/hqdefault.jpg'
        },
        {
          id: 'ScMzIvxBSi4',
          title: `Advanced ${query} Techniques That Actually Work 💡`,
          description: 'Discover professional strategies and advanced methods used by experts. Includes case studies and real-world applications.',
          channelTitle: 'Pro Tips Channel',
          viewCount: '456789',
          likeCount: '23156',
          publishedAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000).toISOString(),
          duration: '18:27',
          thumbnailUrl: 'https://i.ytimg.com/vi/ScMzIvxBSi4/hqdefault.jpg'
        }
      ],
      totalResults: 3
    };
  }

  /**
   * Combine captions into full transcript
   */
  combineCaptions(captions: YouTubeCaption[]): string {
    return captions.map(caption => caption.text).join(' ');
  }

  /**
   * Extract key moments and timestamps from captions with better formatting
   */
  extractKeyMoments(captions: YouTubeCaption[], segmentDuration: number = 30): Array<{timestamp: string, text: string, startTime: number}> {
    const keyMoments: Array<{timestamp: string, text: string, startTime: number}> = [];
    
    let currentSegment: YouTubeCaption[] = [];
    let segmentStartTime = 0;
    let segmentDuration_current = 0;
    
    captions.forEach((caption, index) => {
      if (currentSegment.length === 0) {
        segmentStartTime = caption.start;
      }
      
      currentSegment.push(caption);
      segmentDuration_current += caption.duration;
      
      // Create segment when duration exceeded or last caption
      if (segmentDuration_current >= segmentDuration || index === captions.length - 1) {
        const segmentText = currentSegment
          .map(c => c.text)
          .join(' ')
          .replace(/\s+/g, ' ')
          .trim();
        
        if (segmentText) {
          const timestamp = this.formatTimestamp(segmentStartTime);
          
          keyMoments.push({
            timestamp,
            text: segmentText.length > 120 ? segmentText.substring(0, 120) + '...' : segmentText,
            startTime: segmentStartTime
          });
        }
        
        currentSegment = [];
        segmentDuration_current = 0;
      }
    });
    
    return keyMoments;
  }

  /**
   * Format seconds to timestamp (MM:SS or HH:MM:SS)
   */
  private formatTimestamp(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }

  /**
   * Search for videos and extract captions in one go
   */
  async searchAndExtractCaptions(
    query: string, 
    maxResults: number = 5,
    language: string = 'en'
  ): Promise<Array<{video: YouTubeVideo, captions: YouTubeCaption[], transcript: string}>> {
    try {
      const searchResult = await this.searchVideos(query, maxResults);
      const results = [];

      for (const video of searchResult.videos) {
        try {
          const captions = await this.extractCaptions(video.id, language);
          const transcript = this.combineCaptions(captions);
          
          results.push({
            video,
            captions,
            transcript
          });
        } catch (error) {
          console.warn(`Failed to extract captions for video ${video.id}:`, error);
          // Still include video with empty captions
          results.push({
            video,
            captions: [],
            transcript: ''
          });
        }
      }

      return results;
    } catch (error) {
      console.error('Search and extract captions failed:', error);
      throw error;
    }
  }

  /**
   * Get video metadata by ID
   */
  async getVideoMetadata(videoId: string): Promise<YouTubeVideo | null> {
    try {
      if (!this.apiKey) {
        return null;
      }

      const detailsUrl = 'https://www.googleapis.com/youtube/v3/videos';
      const response = await axios.get(detailsUrl, {
        params: {
          key: this.apiKey,
          id: videoId,
          part: 'snippet,statistics,contentDetails'
        }
      });

      if (!response.data.items || response.data.items.length === 0) {
        return null;
      }

      const item = response.data.items[0];
      return {
        id: item.id,
        title: item.snippet.title,
        description: item.snippet.description,
        channelTitle: item.snippet.channelTitle,
        viewCount: item.statistics.viewCount,
        likeCount: item.statistics.likeCount,
        publishedAt: item.snippet.publishedAt,
        duration: this.parseDuration(item.contentDetails.duration),
        thumbnailUrl: item.snippet.thumbnails.high.url
      };
    } catch (error) {
      console.error('Failed to get video metadata:', error);
      return null;
    }
  }

  /**
   * Get Supadata API rotator status
   */
  getSupadataRotatorStatus() {
    return this.supadataRotator.getStatus();
  }

  /**
   * Force rotation of Supadata API key
   */
  forceSupadataKeyRotation(): string {
    return this.supadataRotator.forceRotate();
  }

  /**
   * Get current Supadata API key info
   */
  getCurrentSupadataKeyInfo(): { key: string; status: string; availableKeys: number } {
    const status = this.supadataRotator.getStatus();
    const currentKey = this.supadataRotator.getCurrentApiKey();
    const keyReport = status.keyHealthReport.find(k => k.keyId.includes(currentKey.slice(-4)));
    
    return {
      key: `...${currentKey.slice(-4)}`,
      status: keyReport?.status || 'unknown',
      availableKeys: status.availableKeys
    };
  }

  /**
   * Reset all Supadata API keys (emergency function)
   */
  resetSupadataKeys(): void {
    this.supadataRotator.resetAllKeys();
  }

  /**
   * Instant rotate Supadata API key with reason
   */
  instantRotateSupadataKey(reason: string): string {
    return this.supadataRotator.instantRotate(reason);
  }
} 