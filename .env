# =============================================================================
# GLOBAL ENVIRONMENT CONFIGURATION
# =============================================================================
# This file contains all environment variables for the entire application.
# Copy this file to .env.local and add your actual API keys.
# =============================================================================

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL="file:./dev.db"

# =============================================================================
# AUTHENTICATION & OAUTH
# =============================================================================
# NextAuth Configuration
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=http://localhost:3000

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# =============================================================================
# AI MODEL API KEYS
# =============================================================================
# OpenRouter API Key (Primary AI Model Provider)
# Get your key from: https://openrouter.ai/keys
OPENROUTER_API_KEY=your_openrouter_api_key_here
NEXT_PUBLIC_OPENROUTER_API_KEY=your_openrouter_api_key_here

# OpenAI API Key (Alternative)
OPENAI_API_KEY=your_openai_api_key_here
NEXT_PUBLIC_OPENAI_API_KEY=your_openai_api_key_here

# Google Gemini API Key
# Get your key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here
GOOGLE_API_KEY=your_google_api_key_here

# =============================================================================
# SEARCH & RESEARCH APIs
# =============================================================================
# Tavily Search API Keys (Web Research) - Array Format
# Get your keys from: https://tavily.com/
# Format: Comma-separated list of API keys for automatic rotation
TAVILY_API_KEYS=your_key_1,your_key_2,your_key_3,your_key_4
NEXT_PUBLIC_TAVILY_API_KEYS=your_key_1,your_key_2,your_key_3,your_key_4

# Google Search API (Alternative Search)
GOOGLE_SEARCH_API_KEY=your_google_search_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_google_search_engine_id_here

# =============================================================================
# YOUTUBE & VIDEO APIs
# =============================================================================
# YouTube Data API Key
# Get your key from: https://console.developers.google.com/
YOUTUBE_API_KEY=your_youtube_api_key_here

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Next.js Configuration
NODE_ENV=development
PORT=3000

# Content Generation Defaults
DEFAULT_CONTENT_TYPE=blog-post
DEFAULT_WORD_COUNT=2000
DEFAULT_BRAND_VOICE=professional

# Performance Settings
WORKFLOW_TIMEOUT=1800000
MAX_RETRIES=2
ENABLE_VERBOSE_LOGGING=true

# Cost Management
MAX_COST_PER_ARTICLE=2.00
ENABLE_COST_ALERTS=true

# Analytics and Monitoring
NEXT_PUBLIC_ANALYTICS_ENABLED=true
NEXT_PUBLIC_COST_TRACKING_ENABLED=true
NEXT_PUBLIC_PERFORMANCE_MONITORING=true

# Development Settings
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_VERBOSE_LOGGING=false
NEXT_PUBLIC_MOCK_MODE=false

# =============================================================================
# OPTIONAL: ADDITIONAL SERVICE CONFIGURATIONS
# =============================================================================
# Add any additional API keys or service configurations here as needed

# Example: Additional AI Providers
# ANTHROPIC_API_KEY=your_anthropic_api_key_here
# COHERE_API_KEY=your_cohere_api_key_here

# Example: Additional Search Providers  
# BING_SEARCH_API_KEY=your_bing_search_api_key_here
# SERPAPI_KEY=your_serpapi_key_here

# =============================================================================
# SECURITY NOTES
# =============================================================================
# 1. Never commit this file with actual API keys
# 2. Use .env.local for local development with real keys
# 3. Use environment variables in production
# 4. Regularly rotate API keys for security
# =============================================================================