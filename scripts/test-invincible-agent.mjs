#!/usr/bin/env node

/**
 * Invincible Agent <PERSON><PERSON>
 * Tests the elite article writing system with existing Gemini service integration
 */

import { executeInvincibleAgent, getInvincibleAnalytics } from '../src/lib/agents/invincible/invincible-agent.js';

console.log('\n🛡️  INVINCIBLE AGENT DEMO');
console.log('=' .repeat(50));

// Demo configuration
const demoConfig = {
  topic: 'The Future of AI in Content Creation',
  wordCount: 1500,
  tone: 'professional and engaging',
  additionalRequirements: 'Include real-world examples and practical implications'
};

console.log('\n📋 Demo Configuration:');
console.log(`   📝 Topic: ${demoConfig.topic}`);
console.log(`   📊 Word Count: ${demoConfig.wordCount}`);
console.log(`   🎭 Tone: ${demoConfig.tone}`);
console.log(`   ✨ Additional: ${demoConfig.additionalRequirements}`);

console.log('\n🔍 System Analytics:');
const analytics = getInvincibleAnalytics();
console.log(`   🏢 System: ${analytics.systemName}`);
console.log(`   📦 Version: ${analytics.version}`);
console.log(`   ⚡ Powered by: ${analytics.powered_by.join(', ')}`);
console.log(`   🤖 Agents: ${analytics.agents.length}`);

console.log('\n👥 Agent Team:');
analytics.agents.forEach((agent, index) => {
  console.log(`   ${index + 1}. ${agent.name} (${agent.role})`);
  console.log(`      🎯 ${agent.specialization}`);
  console.log(`      📈 Performance: ${agent.performance}`);
  console.log(`      ⏱️  Avg Time: ${agent.avgExecutionTime}`);
});

console.log('\n🚀 Starting Article Generation...');
console.log('─'.repeat(50));

const startTime = Date.now();

try {
  const result = await executeInvincibleAgent({
    ...demoConfig,
    onProgress: (progress) => {
      const timestamp = new Date().toLocaleTimeString();
      console.log(`   [${timestamp}] ${progress.stage}: ${progress.message}`);
    }
  });

  const totalTime = Date.now() - startTime;

  if (result.success) {
    console.log('\n✅ ARTICLE GENERATION SUCCESSFUL!');
    console.log('─'.repeat(50));
    
    console.log('\n📊 Generation Statistics:');
    console.log(`   ⏱️  Total Time: ${totalTime}ms`);
    console.log(`   📝 Article Length: ${result.article.length} characters`);
    console.log(`   📈 Word Count: ${result.article.split(' ').length} words`);
    console.log(`   💰 Estimated Cost: ${result.analytics?.totalCost || 'N/A'}`);

    console.log('\n📄 Generated Article Preview:');
    console.log('─'.repeat(50));
    
    // Show first 500 characters of the article
    const preview = result.article.substring(0, 500);
    console.log(preview);
    
    if (result.article.length > 500) {
      console.log('\n   ... (article continues)');
      console.log(`   Full article: ${result.article.length} characters total`);
    }

    console.log('\n🏆 Invincible Metrics:');
    if (result.invincibleMetrics) {
      console.log(`   🎯 Success Rate: ${result.invincibleMetrics.workflows[0].success_rate}`);
      console.log(`   ⭐ Quality Score: ${result.invincibleMetrics.workflows[0].output_quality}`);
      console.log(`   💡 Unique Features: ${result.invincibleMetrics.capabilities.unique_features.length}`);
    }

    console.log('\n🎉 DEMO COMPLETED SUCCESSFULLY!');
    console.log('   The Invincible Agent is ready for production use.');
    
  } else {
    console.log('\n❌ ARTICLE GENERATION FAILED');
    console.log(`   Error: ${result.error}`);
    console.log(`   Fallback: ${result.fallback}`);
  }

} catch (error) {
  console.log('\n💥 DEMO ERROR');
  console.log(`   Error: ${error.message}`);
  console.log('   Please check your configuration and API keys.');
}

console.log('\n' + '='.repeat(50));
console.log('🛡️  INVINCIBLE AGENT DEMO COMPLETE');
console.log('='.repeat(50)); 